<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Broadcasting\PrivateChannel;

class NewRatingNotification extends Notification
{
    use Queueable;

    protected $ticket;
    protected $ratingValue;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\WaitingTicket  $ticket
     * @param  int  $ratingValue
     * @return void
     */
    public function __construct($ticket, $ratingValue)
    {
        $this->ticket = $ticket;
        $this->ratingValue = $ratingValue;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toDatabase($notifiable)
    {
        return [
            'ticket_id'    => $this->ticket->id,
            'client_phone' => $this->ticket->client_phone,
            'rating'       => $this->ratingValue,
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'ticket_id'    => $this->ticket->id,
            'client_phone' => $this->ticket->client_phone,
            'rating'       => $this->ratingValue,
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn()
    {
        return new PrivateChannel('admin.notifications');
    }
}
