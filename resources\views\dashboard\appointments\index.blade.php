<x-app-layout title="{{ $title }}" is-header-blur="true">
    <!-- Main Content Wrapper -->
    <main class="main-content w-full pb-8">
        <div
            class="mt-4 grid grid-cols-12 gap-4 px-[var(--margin-x)] transition-all duration-[.25s] sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="card col-span-12 lg:col-span-12 xl:col-span-12">
                <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                    <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                        {{ $title }}
                    </h2>

                    <div class="flex space-x-2">
                        <!-- Search Input -->
                        <div class="flex items-center" x-data="{ isInputActive: false }">
                            <label class="block">
                                <input x-effect="isInputActive === true && $nextTick(() => { $el.focus() });"
                                    :class="isInputActive ? 'w-32 lg:w-48' : 'w-0'"
                                    class="form-input bg-transparent px-1 text-right transition-all duration-100 placeholder:text-slate-500 dark:placeholder:text-navy-200"
                                    placeholder="Search here..." type="text" />
                            </label>
                            <button @click="isInputActive = !isInputActive"
                                class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                        </div>

                        <!-- Create Appointment Button -->
                        <a href="{{ route('appointments.create') }}?status={{ $status }}"
                            class="btn space-x-2 bg-secondary font-medium text-white hover:bg-secondary-focus focus:bg-secondary-focus active:bg-secondary-focus/90">
                            <i class="fas fa-plus"></i>
                            <span>{{ __('Create Appointment') }}</span>
                        </a>

                        <!-- Export to Excel Button -->
                        <a href="{{ route('appointments.export', ['status' => $status]) }}"
                            class="btn space-x-2 bg-info font-medium text-white hover:bg-info-focus hover:shadow-lg hover:shadow-info/50 focus:bg-info-focus focus:shadow-lg focus:shadow-info/50 active:bg-info-focus/90">
                            <span>{{ __('Export Appointments') }}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                            </svg>
                        </a>
                    </div>
                </div>
                <!-- Cards for Appointment Status Totals -->
                <div class="mt-5 grid grid-cols-1 gap-4 px-4 sm:grid-cols-2 sm:px-5">
                    <!-- Total Assigned Card -->
                    <div
                        class="relative flex flex-col overflow-hidden rounded-lg bg-linear-to-br from-green-400 to-green-600 p-3.5">
                        <p class="text-xs uppercase text-white">{{ __('Total Assigned') }}</p>
                        <div class="flex items-end justify-between space-x-2">
                            <p class="mt-4 text-2xl font-medium text-white">{{ $totalAssigned }}</p>
                            <a href="{{ route('appointments.assigned') }}"
                                class="border-b border-dotted border-current pb-0.5 text-xs font-medium text-white outline-hidden transition-colors duration-300 line-clamp-1 hover:text-white focus:text-white">
                                {{ __('Report') }}
                            </a>
                        </div>
                        <div class="mask is-reuleaux-triangle absolute top-0 right-0 -m-3 size-16 bg-white/20"></div>
                    </div>

                    <!-- Total Waiting Card -->
                    <div
                        class="relative flex flex-col overflow-hidden rounded-lg bg-linear-to-br from-amber-400 to-orange-600 p-3.5">
                        <p class="text-xs uppercase text-amber-50">{{ __('Total Waiting') }}</p>
                        <div class="flex items-end justify-between space-x-2">
                            <p class="mt-4 text-2xl font-medium text-white">{{ $totalWaiting }}</p>
                            <a href="{{ route('appointments.waiting') }}"
                                class="border-b border-dotted border-current pb-0.5 text-xs font-medium text-amber-50 outline-hidden transition-colors duration-300 line-clamp-1 hover:text-white focus:text-white">
                                {{ __('Report') }}
                            </a>
                        </div>
                        <div class="mask is-diamond absolute top-0 right-0 -m-3 size-16 bg-white/20"></div>
                    </div>
                </div>

                <!-- Appointments Table -->
                <div class="scrollbar-sm mt-5 min-w-full overflow-x-auto">
                    <table class="is-hoverable w-full text-center">
                        <thead>
                            <tr class="border-b border-slate-200 dark:border-navy-500">
                                <th class="px-4 py-3">{{ __('Patient Name') }}</th>
                                <th class="px-4 py-3">{{ __('Phone Number') }}</th>
                                <th class="px-4 py-3">{{ __('Birth Date') }}</th>
                                <th class="px-4 py-3">{{ __('National ID') }}</th>
                                <th class="px-4 py-3">{{ __('Clinic') }}</th>
                                <th class="px-4 py-3">{{ __('Payment Method') }}</th>
                                @if (!request()->is('waiting-appointments'))
                                    <th class="px-4 py-3">{{ __('Waiting Number') }}</th>
                                    <th class="px-4 py-3">{{ __('Assigned Doctor') }}</th>
                                @else
                                    <th class="px-4 py-3">{{ __('Requested Doctor') }}</th>
                                @endif
                                <th class="px-4 py-3">{{ __('Status') }}</th>
                                <th class="px-4 py-3"><i class="fa fa-bolt" aria-hidden="true"></i></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($appointments as $appointment)
                                <tr class="border-y border-transparent border-b-slate-200 dark:border-b-navy-500">
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->full_name }}
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->client_phone }}
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->birth_date?->format('Y-m-d') }}
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->identity_number }}
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->clinic }}
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        {{ $appointment->payment_method }}
                                    </td>
                                    @if (!request()->is('waiting-appointments'))
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            {{ $appointment->waiting_number ?? '-' }}
                                        </td>
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            {{ $appointment->doctor_data ? $appointment->doctor_data->name : '-' }}
                                        </td>
                                    @else
                                        <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                            {{ $appointment->doctor }}
                                        </td>
                                    @endif
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        <x-status-badge :status="$appointment->status" />
                                    </td>
                                    <td class="whitespace-nowrap px-4 py-3 sm:px-5">
                                        <!-- Action Buttons -->
                                        <div x-data="{ showModal: false }"
                                            class="btn size-8 p-0 text-error hover:bg-success/20 focus:bg-success/20 active:bg-success/25">
                                            <button @click="showModal = true"
                                                class="btn size-8 p-0 text-success hover:bg-success/20 focus:bg-success/20 active:bg-success/25">
                                                <i class="fa fa-ticket" aria-hidden="true"></i>
                                            </button>
                                            <template x-teleport="#x-teleport-target">
                                                <form
                                                    class="fixed inset-0 z-100 flex flex-col items-center justify-center overflow-hidden px-4 py-6 sm:px-5"
                                                    x-show="showModal" role="dialog" method="post"
                                                    action="{{ route('appointments.assigne-waiting-number', $appointment) }}"
                                                    @keydown.window.escape="showModal = false">
                                                    @csrf @method('put')
                                                    <div class="absolute inset-0 bg-slate-900/60 transition-opacity duration-300"
                                                        @click="showModal = false" x-show="showModal"
                                                        x-transition:enter="ease-out"
                                                        x-transition:enter-start="opacity-0"
                                                        x-transition:enter-end="opacity-100"
                                                        x-transition:leave="ease-in"
                                                        x-transition:leave-start="opacity-100"
                                                        x-transition:leave-end="opacity-0"></div>
                                                    <div class="relative max-w-sm rounded-lg bg-white px-4 pb-4 transition-all duration-300 dark:bg-navy-700 sm:px-5"
                                                        x-show="showModal" x-transition:enter="easy-out"
                                                        x-transition:enter-start="opacity-0 [transform:translate3d(0,-1rem,0)]"
                                                        x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]"
                                                        x-transition:leave="easy-in"
                                                        x-transition:leave-start="opacity-100 [transform:translate3d(0,0,0)]"
                                                        x-transition:leave-end="opacity-0 [transform:translate3d(0,-1rem,0)]">
                                                        <div class="my-3 flex h-8 items-center justify-between">
                                                            <h2
                                                                class="font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100 lg:text-base">
                                                                {{ __('Add Waiting Number') }}
                                                            </h2>
                                                            <button @click="showModal = !showModal" type="button"
                                                                class="btn -mr-1.5 size-7 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    class="size-4.5" fill="none"
                                                                    viewBox="0 0 24 24" stroke="currentColor"
                                                                    stroke-width="2">
                                                                    <path stroke-linecap="round"
                                                                        stroke-linejoin="round"
                                                                        d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                        <div class="mt-4 grid grid-cols-1 gap-4">
                                                            <x-input-text :label="__('Waiting Number')" name="waiting_number"
                                                                value="{{ old('waiting_number', $appointment->waiting_number) }}"
                                                                placeholder="Enter Waiting Number" required />
                                                        </div>
                                                        <div class="mt-4 grid grid-cols-1 gap-4">
                                                            <x-select-input :label="__('Assigned Doctor')" name="doctor_id"
                                                                :options="$doctors->pluck('name', 'id')->toArray()" />
                                                        </div>
                                                        <div class="mt-4 text-right">
                                                            <button type="submit"
                                                                class="btn h-8 rounded-full bg-primary text-xs-plus font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                                                                {{ __('Save and Send') }}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </template>
                                        </div>
                                        <a href="{{ route('appointments.edit', $appointment->id) }}?status={{ $status }}"
                                            class="btn size-8 p-0 text-info hover:bg-info/20 focus:bg-info/20 active:bg-info/25">
                                            <i class="fa fa-edit" aria-hidden="true"></i>
                                        </a>
                                        <form
                                            action="{{ route('appointments.destroy', $appointment->id) }}?status={{ $status }}"
                                            method="POST" class="inline-block ml-2">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                class="btn size-8 p-0 text-error hover:bg-error/20 focus:bg-error/20 active:bg-error/25">
                                                <i class="fa fa-trash" aria-hidden="true"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination & Entry Count -->
                <div>
                    {{ $appointments->links() }}
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
