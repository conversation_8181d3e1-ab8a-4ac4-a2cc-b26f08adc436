<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddScheduleColumnsToClinicsTable extends Migration
{
    public function up()
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->string('morning_shift')->nullable()->after('name'); // e.g., "8 ص - 12 م"
            $table->string('evening_shift')->nullable()->after('morning_shift'); // e.g., "4 م - 10 م"
            $table->string('closed_day')->nullable()->after('evening_shift'); // e.g., "الجمعة"
        });
    }

    public function down()
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->dropColumn(['morning_shift', 'evening_shift', 'closed_day']);
        });
    }
}
