<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Appointment;

class AppointmentsTableSeeder extends Seeder
{
    public function run()
    {
        // Create 15 random appointments using the factory
        Appointment::factory(15)->create();

        // Optionally create a few with a known status
        Appointment::factory()->create([
            'patient_name'  => '<PERSON>',
            'status'        => 'confirmed',
            'notes'         => 'Seeded appointment for <PERSON>',
        ]);

        $this->command->info('Appointments seeded.');
    }
}
