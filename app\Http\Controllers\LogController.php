<?php

namespace App\Http\Controllers;

use App\Models\Log;
use Illuminate\Http\Request;

class LogController extends Controller
{
    // Display a paginated list of logs
    public function index(Request $request)
    {
        $title = __("System Logs");
        // Restrict access: only Super Admins can view logs
        if (! $request->user()->hasRole('Super Admin')) {
            abort(403, 'Unauthorized action.');
        }

        $logs = Log::with('user')->orderBy('created_at', 'desc')->paginate(15);
        return view('dashboard.logs.index', compact('logs', 'title'));
    }
}
