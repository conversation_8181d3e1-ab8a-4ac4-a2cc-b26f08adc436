@if ($paginator->hasPages())
<div class="flex flex-col justify-between space-y-4 px-4 py-4 sm:flex-row sm:items-center sm:space-y-0 sm:px-5">
    <div class="text-xs-plus">
        {{-- You can display a custom message if needed, or use the paginator methods --}}
        Showing {{ $paginator->firstItem() }} - {{ $paginator->lastItem() }} of {{ $paginator->total() }} entries
    </div>

    <ol class="pagination">
        @if ($paginator->onFirstPage())
            <li class="rounded-l-full bg-slate-150 dark:bg-navy-500">
                <span class="flex size-8 items-center justify-center rounded-full text-slate-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </span>
            </li>
        @else
            <li class="rounded-l-full bg-slate-150 dark:bg-navy-500">
                <a href="{{ $paginator->previousPageUrl() }}"
                   class="flex size-8 items-center justify-center rounded-full text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
            </li>
        @endif

        {{-- Pagination Elements --}}
        @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
                <li class="bg-slate-150 dark:bg-navy-500"><span class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3">{{ $element }}</span></li>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <li class="bg-slate-150 dark:bg-navy-500">
                            <span class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 bg-primary text-white">
                                {{ $page }}
                            </span>
                        </li>
                    @else
                        <li class="bg-slate-150 dark:bg-navy-500">
                            <a href="{{ $url }}" class="flex h-8 min-w-[2rem] items-center justify-center rounded-full px-3 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                                {{ $page }}
                            </a>
                        </li>
                    @endif
                @endforeach
            @endif
        @endforeach

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <li class="rounded-r-full bg-slate-150 dark:bg-navy-500">
                <a href="{{ $paginator->nextPageUrl() }}"
                   class="flex size-8 items-center justify-center rounded-full text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </li>
        @else
            <li class="rounded-r-full bg-slate-150 dark:bg-navy-500">
                <span class="flex size-8 items-center justify-center rounded-full text-slate-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </span>
            </li>
        @endif
    </ol>
</div>
@endif
