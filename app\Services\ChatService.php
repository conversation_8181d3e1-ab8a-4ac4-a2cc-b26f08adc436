<?php

namespace App\Services;

use App\Models\Clinic;
use App\Models\ConversationLog;
use App\Models\ConversationState;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

class ChatService
{
    /*
     * The required slots for the new appointment form.
     */
    protected $requiredSlots = [
        'full_name',         // must be 4+ words
        'birth_date',        // expects format YYYY-MM-DD
        'identity_number',   // 10-15 digits
        'clinic',            // matched from available clinics in DB
        'doctor',            // optional: if user says "لا"/"none", then empty string
        'appointment_date',  // expects format YYYY-MM-DD
        'shift',             // either "الصباحية" or "المسائية"
        'payment_method'     // either "تامين"/"تأمين" or "كاش"
    ];

    /**
     * Human-friendly Arabic labels for each slot key.
     */
    protected $slotLabels = [
        'full_name'        => 'الاسم الكامل',
        'birth_date'       => 'تاريخ الميلاد',
        'identity_number'  => 'رقم الهوية',
        'clinic'           => 'اسم العيادة',
        'doctor'           => 'اسم الطبيب',
        'appointment_date' => 'تاريخ الموعد',
        'shift'            => 'الفترة',
        'payment_method'   => 'طريقة الدفع',
    ];

    /*
     * Available clinic names loaded from the DB.
     */
    protected $availableSpecialties = [];

    /*
     * Available doctor names loaded from the DB.
     */
    protected $availableDoctors = [];

    /*
     * DeepSeek API configuration.
     */
    protected $deepseekConfig = [
        'api_key'     => '',
        'api_url'     => 'https://api.deepseek.com/v1/chat/completions',
        'model'       => 'deepseek-chat',
        'temperature' => 0.2,
        'max_tokens'  => 300
    ];

    public function __construct()
    {
        // Load clinics from the database
        try {
            $this->availableSpecialties = DB::table('clinics')->pluck('name')->toArray();
            Log::info("Loaded available specialties: " . json_encode($this->availableSpecialties));
        } catch (\Exception $e) {
            Log::error("Error fetching clinics: " . $e->getMessage());
            $this->availableSpecialties = [];
        }

        // Load doctors from the database
        try {
            $this->availableDoctors = DB::table('doctors')->pluck('name')->toArray();
            Log::info("Loaded available doctors: " . json_encode($this->availableDoctors));
        } catch (\Exception $e) {
            Log::error("Error fetching doctors: " . $e->getMessage());
            $this->availableDoctors = [];
        }

        // Load DeepSeek API key from config
        $this->deepseekConfig['api_key'] = config('services.deepseek.api_key');
        Log::info("DeepSeek API key: " . $this->deepseekConfig['api_key']);
    }

    /**
     * Initialize a brand-new conversation state.
     */
    public function initializeConversationState()
    {
        $slots = [];
        foreach ($this->requiredSlots as $slot) {
            $slots[$slot] = null;
        }

        $state = [
            'last_updated'         => Carbon::now(),
            'slots'                => $slots,
            'history'              => [],
            'current_step'         => 'main_menu',
            'booking_id'           => null,
            'off_topic_count'      => 0,
            'last_intent'          => null,
            'intent_confidence'    => 0,
            'detected_entities'    => [],
            'client_phone'         => null,
            'handoff'              => false,
            'awaiting_confirmation' => false,
            'confirmed'            => false,
            'handoff_acknowledged' => false,
        ];

        // ←— **ADD THESE** —————————————
        $state['pending_messages']  = [];                                   // start with no buffered messages
        $state['last_message_time'] = Carbon::now()->subSeconds(2);         // pretend last message was 2s ago
        // ————————————————————————————————

        Log::debug("Initialized conversation state: " . json_encode($state));
        return $state;
    }


    /**
     * Retrieve the conversation state for a given user.
     */
    public function getState($senderId)
    {
        $stateRecord = ConversationState::find($senderId);
        if ($stateRecord) {
            $state = $stateRecord->state;

            // ——— back‐fill new fields if missing ———
            $defaults = [
                'handoff_acknowledged' => false,
                'pending_messages'     => [],
                'last_message_time'    => Carbon::now()->subSeconds(2),
            ];
            foreach ($defaults as $k => $v) {
                if (! array_key_exists($k, $state)) {
                    $state[$k] = $v;
                }
            }
            // save once so future loads are complete
            $this->saveState($senderId, $state);
            // ————————————————————————————————————

            // your existing phone‐fill logic...
            if (empty($state['client_phone'])) {
                $state['client_phone'] = $senderId;
                $this->saveState($senderId, $state);
            }
            return $state;
        }

        // no record → brand new
        $state = $this->initializeConversationState();
        $state['client_phone'] = $senderId;
        ConversationState::create([
            'sender_id' => $senderId,
            'state'     => $state,
        ]);
        return $state;
    }


    /**
     * Save updated conversation state.
     */
    public function saveState($senderId, $state)
    {
        ConversationState::updateOrCreate(
            ['sender_id' => $senderId],
            ['state'     => $state]
        );
    }

    /**
     * Append a message to the conversation history and log it.
     */
    public function updateConversation($sender, $speaker, $message, &$state)
    {
        $state['last_updated'] = Carbon::now();
        $state['history'][]   = ['sender' => $speaker, 'message' => $message];

        Log::debug("Updated conversation history for sender: $sender. New message from $speaker: $message");
        try {
            ConversationLog::create([
                'sender_id' => $sender,
                'message'   => $message,
                'speaker'   => $speaker,
                'timestamp' => Carbon::now(),
            ]);
        } catch (\Exception $e) {
            Log::error("Error logging conversation: " . $e->getMessage());
        }
    }

    /**
     * Check if conversation has timed out.
     */
    public function conversationTimedOut($state)
    {
        $timeoutMinutes = 30;
        $diff = Carbon::now()->diffInMinutes($state['last_updated']);
        Log::debug("Time since last update: $diff minutes");
        return ($diff > $timeoutMinutes);
    }

    /**
     * Basic fuzzy matching.
     */
    public function fuzzyMatch($word, array $wordList, $threshold = 50)
    {
        $bestMatch = null;
        $bestScore = $threshold;
        foreach ($wordList as $target) {
            similar_text($word, $target, $percent);
            Log::debug("Comparing '$word' with '$target' yields similarity: $percent");
            if ($percent > $bestScore) {
                $bestMatch = $target;
                $bestScore = $percent;
            }
        }
        Log::debug("Fuzzy match result for '$word' is: " . ($bestMatch ?: "none") . " with score: $bestScore");
        return $bestMatch;
    }

    /**
     * Call DeepSeek with conversation context and system instructions.
     */
    /**
     * Call DeepSeek with conversation context and system instructions,
     * including the dynamic list of valid clinics and doctors.
     */
    public function callDeepSeek(array $state, string $userMessage): array
    {
        // Build dynamic lists
        // Build dynamic lists
        $clinicsList = implode(', ', $this->availableSpecialties);
        $doctorsList = implode(', ', $this->availableDoctors);

        // Compose the system prompt with added style instructions
        $systemPrompt = <<<PROMPT
أنت مساعد ذكي لمجمع المختار الطبي. أجب دائماً بطريقة ودودة ومفيدة وبلهجة سعودية خفيفة، واستخدم الإيموجي لتوضيح المزاج (مثلاً 👍، 😊، 😉).

📌 مهامك:
1. حجز المواعيد (اجمع: full_name, birth_date, identity_number, clinic, doctor, appointment_date, shift, payment_method)
   — **أخيراً** أرسل:
     "intent": "booking"
2. عرض العيادات والأطباء.
3. مشاركة رابط العروض (https://yoursite.com/offers).
4. مشاركة رابط الموقع/Linktree إذا طلب المستخدم "location" أو "رابط".
5. عند طلب تواصل مع إنسان، استعمل `"action": "handoff_to_human"`.
6. إذا طلب المستخدم "كل العيادات" أو "جميع العيادات"، استخدم intent `"list_clinics"` ولا تُرسل حقل `"clinic"`.
7. إذا طلب معلومات عن عيادة معيّنة، استخدم intent `"clinic_info"` مع `{ "clinic": "اسم العيادة" }`.
8. إذا طلب معلومات عن الأطباء، استخدم intent `"list_doctors"` أو `"doctor_info"`.

– القوائم الصالحة:
    • العيادات المتاحة: $clinicsList
    • الأطباء المتاحون: $doctorsList

⚠️ للتنبيه: عند حجز موعد، فقط أسماء العيادات والأطباء الواردة أعلاه مسموح بها. إذا ذكر المستخدم غيرها، حاول تعديله لأقرب تطابق أو اطلب توضيحاً.

+ 9. إذا بدأ المستخدم بتحية (مثلاً "مرحبا"، "السلام عليكم"، "هلا")، استخدم intent "greeting"
+    و"assistant_reply" يجب أن يكون:
+    "ياهلا وغلا فيك في مجمع المختار الطبي 💙! نورتنا ✨
+     🔹 تقدر تسـأل عن:
+        • حجز موعد 🗓️
+        • مواعيد العيادات والأطباء 👩‍⚕️👨‍⚕️
+        • أحدث العروض 💰
+        • موقعنا وحساباتنا على الخريطة والسوشال ميديا 📍
+
+     اكتب لي طلبك بكل بساطة وأنا بالخدمة 🤖"

أرسل الرد كـ JSON فقط بصيغة:
{
  "intent": "...",
  "assistant_reply": "...",
  "entities": { ... },
  "action": null
}
PROMPT;

        // Build conversation history context
        $recentHistory = array_slice($state['history'], -10);
        $conversationHistory = "";
        foreach ($recentHistory as $entry) {
            $role = ($entry['sender'] === 'User') ? "User" : "Assistant";
            $conversationHistory .= "$role: {$entry['message']}\n";
        }

        // Show currently filled booking slots (if any)
        $filledSlots = [];
        foreach ($state['slots'] as $slot => $val) {
            if (!empty($val)) {
                $filledSlots[] = "$slot = $val";
            }
        }
        $slotInfo = empty($filledSlots)
            ? "لم يتم ملء أي حقل بعد."
            : "الحقول المعبأة: " . implode("; ", $filledSlots);

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'system', 'content' => "سياق المحادثة:\n$conversationHistory\n\n$slotInfo"],
            ['role' => 'user', 'content' => $userMessage],
        ];

        $payload = [
            'model'       => $this->deepseekConfig['model'],
            'messages'    => $messages,
            'temperature' => 0.7,
            'max_tokens'  => $this->deepseekConfig['max_tokens'],
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->deepseekConfig['api_key'],
                'Content-Type'  => 'application/json',
            ])->post($this->deepseekConfig['api_url'], $payload);

            if (!$response->successful()) {
                Log::warning("DeepSeek API error: " . $response->body());
                return [
                    'intent'          => 'unknown',
                    'assistant_reply' => "عذراً، حدث خطأ. حاول لاحقاً.",
                    'entities'        => [],
                    'action'          => null
                ];
            }

            $data    = $response->json();
            $rawText = $data['choices'][0]['message']['content'] ?? '';

            // Extract JSON from the raw text
            if (preg_match('/\{.*\}/s', $rawText, $matches)) {
                $parsed = json_decode($matches[0], true);
                if (isset($parsed['assistant_reply'])) {
                    return $parsed;
                }
            }
            Log::warning("Could not parse valid JSON from DeepSeek. Raw: $rawText");
            return [
                'intent'          => 'unknown',
                'assistant_reply' => $rawText,
                'entities'        => [],
                'action'          => null
            ];
        } catch (\Exception $e) {
            Log::error("DeepSeek error: " . $e->getMessage());
            return [
                'intent'          => 'unknown',
                'assistant_reply' => "عذراً، حدث خطأ في الخدمة.",
                'entities'        => [],
                'action'          => null
            ];
        }
    }
    /**
     * Analyse the user message, update the conversation state,
     * and return the assistant’s reply.
     *
     * @param array  $state       – mutable conversation state (will be saved)
     * @param string $userMessage – raw WhatsApp text from the client
     * @param string $clientPhone – the sender’s phone number (ID)
     *
     * @return string Assistant reply to send back over WhatsApp
     */
    public function generateSmartResponse(array &$state, string $userMessage, string $clientPhone): string
    {
        // 0) Normalise & log the incoming message
        $normMsg = $this->normalizeArabic($userMessage);
        $this->updateConversation($clientPhone, 'User', $userMessage, $state);

        /* ------------------------------------------------------------------
     | 1) Short-circuit: Human hand-off already in progress              |
     ------------------------------------------------------------------*/
        if (!empty($state['handoff'])) {
            if (!($state['handoff_acknowledged'] ?? false)) {
                $state['handoff_acknowledged'] = true;
                $this->saveState($clientPhone, $state);

                $reply = "جاري التحويل إلى موظف بشري، يرجى الانتظار قليلاً…";
                $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
                return $reply;
            }
            // Do nothing else while a human is handling the chat
            return '';
        }

        /* ------------------------------------------------------------------
     | 3) Rate-limit ONLY the LLM calls (DeepSeek)                       |
     ------------------------------------------------------------------*/
        $rateKey = "deepseek:{$clientPhone}";
        if (RateLimiter::tooManyAttempts($rateKey, 1)) {
            $reply = "لحظة من فضلك، جاري معالجة طلبك السابق...";
            $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
            return $reply;
        }
        RateLimiter::hit($rateKey, 2);

        /* ------------------------------------------------------------------
     | 4) Pre-extract simple entities, then query DeepSeek               |
     ------------------------------------------------------------------*/
        $this->extractEntitiesLocally($state, $userMessage);

        $deep = $this->callDeepSeek($state, $userMessage);
        $intent         = $deep['intent']          ?? 'unknown';
        $assistantReply = $deep['assistant_reply'] ?? "لم أفهم طلبك.";
        $entities       = $deep['entities']        ?? [];
        $action         = $deep['action']          ?? null;

        $this->processDetectedEntities($state, $entities);

        /* ------------------------------------------------------------------
     | 5) Pending-confirmation branch                                    |
     ------------------------------------------------------------------*/
        if ($state['awaiting_confirmation']) {
            if (in_array($normMsg, ['تأكيد', 'تاكيد', 'confirm'], true)) {
                $state['confirmed']             = true;
                $state['awaiting_confirmation'] = false;
            } elseif (in_array($normMsg, ['تعديل', 'edit'], true)) {
                $state['awaiting_confirmation'] = false;
                $this->saveState($clientPhone, $state);
                $reply = "حسنًا، أي حقل تريد تعديله؟";
                $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
                return $reply;
            } else {
                $reply = 'بانتظار ردك بـ **تأكيد** أو *تعديل* 😉';
                $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
                return $reply;
            }
        }

        /* ------------------------------------------------------------------
     | 6) Human hand-off requested by DeepSeek                           |
     ------------------------------------------------------------------*/
        if ($action === 'handoff_to_human') {
            $state['handoff']             = true;
            $state['handoff_acknowledged'] = false;
            $this->saveState($clientPhone, $state);

            $reply = "حاضر، بحولك لأحد موظفينا البشرية 😊";
            $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
            return $reply;
        }

        /* ------------------------------------------------------------------
     | 7) Non-booking fallback                                           |
     ------------------------------------------------------------------*/
        if (!in_array($intent, ['booking', 'book_appointment'], true)) {
            $this->updateConversation($clientPhone, 'Assistant', $assistantReply, $state);
            $this->saveState($clientPhone, $state);
            return $assistantReply;
        }

        /* ------------------------------------------------------------------
     | 8) Booking flow: collect / confirm fields                         |
     ------------------------------------------------------------------*/
        // a) start booking
        if (array_reduce($state['slots'], fn($allNull, $v) => $allNull && is_null($v), true)) {
            $state['current_step'] = 'booking_started';
            $this->saveState($clientPhone, $state);

            $reply = "حياك الله! 😊 بنبدأ بحجز الموعد.\n"
                . "أرسل لي المعلومات التالية:\n"
                . "• اسمك الكامل\n• تاريخ ميلادك\n• رقم هويتك\n"
                . "• العيادة\n• الطبيب (إن وجد)\n• التاريخ\n• الفترة\n• طريقة الدفع";
            $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
            return $reply;
        }

        // b) ask for missing mandatory slots (doctor optional)
        $required = array_filter($this->requiredSlots, fn($s) => $s !== 'doctor');
        $missing  = array_filter($required, fn($s) => empty($state['slots'][$s]));
        // map each slot-key to its Arabic label (fall back to key if missing)
        $labels = array_map(fn($slot) => $this->slotLabels[$slot] ?? $slot, $missing);
        if (in_array('clinic', $missing, true)) {
            $this->updateConversation($clientPhone, 'Assistant', $this->handleClinicList(), $state);
            $this->saveState($clientPhone, $state);
            return $this->handleClinicList();
        }
        if ($missing) {
            $reply  = "بعد إذنك، أحتاج: " . implode(' و ', $labels) . " 🙏";
            $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
            $this->saveState($clientPhone, $state);
            return $reply;
        }

        // c) summarise & confirm
        if (!$state['confirmed']) {
            $state['awaiting_confirmation'] = true;
            $this->saveState($clientPhone, $state);

            $summary = "🔖 هذه بياناتك:\n";
            foreach ($state['slots'] as $k => $v) {
                $summary .= "• $k: " . ($k === 'doctor' && $v === '' ? 'بدون' : $v) . "\n";
            }
            $reply = $summary . "اكتب **تأكيد** للحجز أو *تعديل*.";
            $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
            return $reply;
        }

        // d) save booking
        $bookingId = $this->saveBookingToDb(array_merge($state['slots'], [
            'client_phone' => $clientPhone
        ]), $clientPhone);

        $reply = $bookingId
            ? "✅ تم تأكيد الحجز! رقم التذكرة: $bookingId 🙌"
            : "عذراً، حصل خطأ أثناء الحجز. حاول مرة ثانية أو اتصل بنا 📞";

        $this->updateConversation($clientPhone, 'Assistant', $reply, $state);
        $this->saveState($clientPhone, $state);
        return $reply;
    }





    /**
     * Handle the case where the user asks for all clinics.
     */
    private function handleClinicList(): string
    {
        $clinics = Clinic::all(['name', 'description']);
        if ($clinics->isEmpty()) {
            return "عذراً، لا توجد عيادات متاحة حالياً.";
        }
        $reply = "هذه أسماء عياداتنا المتوفرة:\n";
        foreach ($clinics as $c) {
            $reply .= "• {$c->name}";
            if ($c->description) {
                $reply .= " - {$c->description}";
            }
            $reply .= "\n";
        }
        $reply .= "\nللحصول على معلومات عن عيادة محددة، اذكر اسم العيادة.";
        return $reply;
    }

    /**
     * Handle showing detailed info for a specific clinic.
     */
    private function handleClinicInfo(array &$state, array $entities): string
    {
        $clinicName = $entities['clinic'] ?? '';
        if (empty($clinicName)) {
            // If deepseek didn’t return a specific name, fall back to list.
            return $this->handleClinicList();
        }

        $clinic = Clinic::where('name', $clinicName)
            ->first(['id', 'name', 'description', 'morning_shift', 'evening_shift', 'closed_day']);

        if (!$clinic) {
            return "عذراً، لا توجد عيادة باسم $clinicName في نظامنا.";
        }

        $msg = "هذه معلومات عيادة {$clinic->name}:\n";
        if ($clinic->description) {
            $msg .= "وصف: {$clinic->description}\n";
        }
        $msg .= "الفترة الصباحية: {$clinic->morning_shift}\n";
        $msg .= "الفترة المسائية: {$clinic->evening_shift}\n";
        $msg .= "يوم الإغلاق: {$clinic->closed_day}\n";

        $doctors = DB::table('doctors')
            ->where('clinic_id', $clinic->id)
            ->get(['name', 'schedule']);

        if ($doctors->isNotEmpty()) {
            $msg .= "\nالأطباء المتاحون:\n";
            foreach ($doctors as $doc) {
                $msg .= "• د. {$doc->name} - {$doc->schedule}\n";
            }
        }
        return $msg;
    }

    /**
     * Handle the case where the user asks for all doctors.
     */
    private function handleDoctorList(): string
    {
        $doctors = DB::table('doctors')->get(['name', 'clinic_id', 'schedule']);
        if ($doctors->isEmpty()) {
            return "عذراً، لا يوجد أطباء حالياً.";
        }
        $reply = "قائمة الأطباء المتاحين:\n";
        foreach ($doctors as $doc) {
            $clinicName = DB::table('clinics')->where('id', $doc->clinic_id)->value('name');
            $reply .= "• د. {$doc->name} (عيادة: {$clinicName}) - {$doc->schedule}\n";
        }
        $reply .= "\nيمكنك ذكر اسم الطبيب لمعرفة التفاصيل.";
        return $reply;
    }

    /**
     * Handle showing detailed info for a specific doctor.
     */
    private function handleDoctorInfo(array &$state, array $entities): string
    {
        $doctorName = trim($entities['doctor'] ?? '');
        if (empty($doctorName)) {
            return $this->handleDoctorList();
        }

        $doctor = DB::table('doctors')
            ->where('name', $doctorName)
            ->first(['name', 'clinic_id', 'schedule']);

        if (!$doctor) {
            return "عذراً، لا يوجد طبيب باسم $doctorName.";
        }

        $clinicName = DB::table('clinics')
            ->where('id', $doctor->clinic_id)
            ->value('name');

        $reply = "تفاصيل الدكتور {$doctor->name}:\n";
        $reply .= "العيادة: {$clinicName}\n";
        $reply .= "جدول الدوام: {$doctor->schedule}\n";
        return $reply;
    }

    /**
     * Process detected entities from DeepSeek and store them in state.
     */
    private function processDetectedEntities(array &$state, array $entities)
    {
        foreach ($entities as $entityName => $entityValue) {
            Log::debug("Processing entity '$entityName' with value: $entityValue");

            switch ($entityName) {

                case 'full_name':
                    if ($this->validateFullName($entityValue)) {
                        $state['slots']['full_name'] = $entityValue;
                    }
                    break;

                case 'identity_number':
                    if ($this->validateIdentityNumber($entityValue)) {
                        $state['slots']['identity_number'] = $entityValue;
                    }
                    break;

                case 'birth_date':
                    $raw = trim($entityValue);
                    if ($raw) {
                        try {
                            $d = Carbon::parse($raw)->format('Y-m-d');
                            $state['slots']['birth_date'] = $d;
                            Log::debug("Parsed birth_date: $raw => $d");
                        } catch (\Exception $e) {
                            Log::debug("Birth date parse failed for: $raw");
                        }
                    }
                    break;

                case 'appointment_date':
                    $raw = trim(mb_strtolower($entityValue));
                    try {
                        switch ($raw) {
                            case 'اليوم':
                                $date = Carbon::today();
                                break;
                            case 'غدا':
                            case 'غداً':
                                $date = Carbon::tomorrow();
                                break;
                            case 'الاثنين القادم':
                            case 'الإثنين القادم':
                                $date = Carbon::parse('next monday');
                                break;
                            default:
                                $date = Carbon::parse($entityValue);
                        }
                        $state['slots']['appointment_date'] = $date->format('Y-m-d');
                        Log::debug("Parsed appointment_date: $entityValue => " . $date->format('Y-m-d'));
                    } catch (\Exception $e) {
                        Log::warning("Could not parse appointment_date '{$entityValue}': {$e->getMessage()}");
                        $state['slots']['appointment_date'] = null;
                    }
                    break;

                case 'clinic':
                    $norm = $this->normalizeArabic($entityValue);

                    // 1) exact match
                    $found = null;
                    foreach ($this->availableSpecialties as $spec) {
                        if ($norm === $this->normalizeArabic($spec)) {
                            $found = $spec;
                            break;
                        }
                    }
                    if ($found) {
                        $state['slots']['clinic'] = $found;
                        break;
                    }

                    // 2) fuzzy match at 80% threshold
                    $matched = $this->fuzzyMatch(
                        $norm,
                        array_map([$this, 'normalizeArabic'], $this->availableSpecialties),
                        80
                    );
                    if ($matched) {
                        $state['slots']['clinic'] = $matched;
                    }
                    // otherwise leave null so missing‐slot logic will ask user to pick
                    break;

                case 'doctor':
                    $in = mb_strtolower(trim($entityValue));
                    if (in_array($in, ['لا', 'none'], true)) {
                        $state['slots']['doctor'] = '';
                    } else {
                        $md = $this->fuzzyMatch(
                            $in,
                            array_map('mb_strtolower', $this->availableDoctors),
                            50
                        );
                        $state['slots']['doctor'] = $md ?: $entityValue;
                    }
                    break;

                case 'shift':
                    $in = $this->normalizeArabic($entityValue);
                    if (in_array($in, ['الصباحية', 'صباحية', 'صباحيه', 'صباحي'], true)) {
                        $state['slots']['shift'] = 'صباحية';
                    } elseif (in_array($in, ['المسائية', 'مسائية', 'مسائيه', 'مسائي'], true)) {
                        $state['slots']['shift'] = 'مسائية';
                    }
                    break;

                case 'payment_method':
                    $in  = $this->normalizeArabic($entityValue);
                    $map = [
                        'نقدي'         => 'نقدي',
                        'كاش'          => 'نقدي',
                        'بطاقة ائتمان' => 'بطاقة ائتمان',
                        'بطاقه ائتمان' => 'بطاقة ائتمان',
                        'تامين'        => 'تأمين',
                        'تأمين'        => 'تأمين',
                    ];
                    if (isset($map[$in])) {
                        $state['slots']['payment_method'] = $map[$in];
                    }
                    break;

                default:
                    Log::debug("Entity '$entityName' not processed or recognized.");
                    break;
            }
        }
    }



    private function validateFullName($name)
    {
        $words = preg_split('/\s+/', trim($name));
        $valid = (count($words) >= 4 && !preg_match('/\d/', $name));
        Log::debug("Validating full name '$name': " . ($valid ? "valid" : "invalid"));
        return $valid;
    }

    private function validateIdentityNumber($id)
    {
        $valid = preg_match('/^\d{10,15}$/', $id);
        Log::debug("Validating identity number '$id': " . ($valid ? "valid" : "invalid"));
        return $valid;
    }

    /**
     * Save booking data into the waiting_tickets table.
     */
    public function saveBookingToDb($bookingData, $clientPhone)
    {
        try {
            Log::info("Booking phone: " . $clientPhone);
            $bookingId = DB::table('waiting_tickets')->insertGetId([
                'client_phone'     => $clientPhone,
                'full_name'        => $bookingData['full_name'],
                'birth_date'       => $bookingData['birth_date'],
                'identity_number'  => $bookingData['identity_number'],
                'clinic'           => $bookingData['clinic'],
                'doctor'           => $bookingData['doctor'],
                'appointment_date' => $bookingData['appointment_date'],
                'shift'            => $bookingData['shift'],
                'payment_method'   => $bookingData['payment_method'],
                'status'           => 'waiting',
                'created_at'       => now(),
                'updated_at'       => now(),
            ]);
            Log::info("New booking saved with ID: " . $bookingId);

            // Optionally notify staff
            $users = \App\Models\User::all();
            foreach ($users as $user) {
                $user->notify(new \App\Notifications\NewWaitingTicketNotification($bookingData, $user->id));
            }
            return $bookingId;
        } catch (\Exception $e) {
            Log::error("DB error saving booking: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Retrieve offers from the DB.
     */
    public function getOffers()
    {
        try {
            return DB::table('offers')
                ->select('id', 'title', 'price')
                ->orderBy('id', 'asc')
                ->get();
        } catch (\Exception $e) {
            Log::error("Error fetching offers: " . $e->getMessage());
            return collect([]);
        }
    }

    /**
     * Retrieve a clinic description from the DB.
     */
    public function getClinicDescription($clinicName)
    {
        try {
            $description = DB::table('clinics')
                ->where('name', $clinicName)
                ->value('description');
            return $description ?: "لا يوجد وصف متاح لهذا التخصص.";
        } catch (\Exception $e) {
            Log::error("Error fetching clinic description: " . $e->getMessage());
            return "لا يوجد وصف متاح لهذا التخصص.";
        }
    }

    /**
     * Utility to normalize Arabic text for fuzzy matching.
     */
    public function normalizeArabic($text)
    {
        $text = trim($text);
        $text = mb_strtolower($text, 'UTF-8');
        $text = str_replace("ـ", "", $text);
        $text = str_replace(["أ", "إ", "آ"], "ا", $text);
        $text = str_replace(["ى"], "ي", $text);
        $diacritics = ["َ", "ً", "ُ", "ٌ", "ِ", "ٍ", "ْ", "ّ"];
        return str_replace($diacritics, "", $text);
    }

    /**
     * يحوّل عبارات التاريخ العربية الشائعة إلى كائن Carbon.
     * يرجّع null إذا تعذّر الفهم.
     */
    private function parseRelativeDate(string $txt): ?Carbon
    {
        $txt = $this->normalizeArabic($txt);

        // كلمات مباشرة
        $quick = [
            'اليوم'          => Carbon::today(),
            'غدا'            => Carbon::tomorrow(),
            'بعد بكرة'       => Carbon::today()->addDays(2),
            'بعد غد'         => Carbon::today()->addDays(2),
            'الاسبوع القادم' => Carbon::today()->addWeek(),
            'الشهر القادم'   => Carbon::today()->addMonth(),
        ];
        foreach ($quick as $k => $v) {
            if (strpos($txt, $this->normalizeArabic($k)) !== false) {
                return $v;
            }
        }

        // “الاربعاء الجاي / القادم”
        if (preg_match('/(الاحد|الاثنين|الثلاثاء|الاربعاء|الخميس|الجمعة|السبت)\s+(?:القادم|الجاي)/u', $txt, $m)) {
            $en = [
                'الاحد' => 'sunday',
                'الاثنين' => 'monday',
                'الثلاثاء' => 'tuesday',
                'الاربعاء' => 'wednesday',
                'الخميس' => 'thursday',
                'الجمعة' => 'friday',
                'السبت' => 'saturday'
            ][$m[1]];
            return Carbon::parse("next $en");
        }
        return null;
    }

    /**
     * Very fast, regex-based entity extraction used **before** calling DeepSeek.
     * It only fills empty slots – anything already set or later provided by the LLM wins.
     */
    private function extractEntitiesLocally(array &$state, string $msg): void
    {
        // full name: ≥4 Arabic words, no digits
        if (
            empty($state['slots']['full_name']) &&
            preg_match('/\b([\p{Arabic}]{2,}(?:\s+[\p{Arabic}]{2,}){3,})\b/u', $msg, $m)
        ) {
            $state['slots']['full_name'] = trim($m[1]);
        }

        // national ID: 10-15 consecutive digits
        if (
            empty($state['slots']['identity_number']) &&
            preg_match('/\b(\d{10,15})\b/', $msg, $m)
        ) {
            $state['slots']['identity_number'] = $m[1];
        }

        // ISO date  YYYY-MM-DD
        if (
            empty($state['slots']['birth_date']) &&
            preg_match('/\b(\d{4}-\d{1,2}-\d{1,2})\b/', $msg, $m)
        ) {
            $state['slots']['birth_date'] = Carbon::parse($m[1])->format('Y-m-d');
        }

        // clinic (simple fuzzy pass)
        if (empty($state['slots']['clinic'])) {
            foreach ($this->availableSpecialties as $spec) {
                if (mb_stripos($msg, $this->normalizeArabic($spec)) !== false) {
                    $state['slots']['clinic'] = $spec;
                    break;
                }
            }
        }

        // shift
        if (
            empty($state['slots']['shift']) &&
            preg_match('/\b(صباحي(?:ة)?|مسائي(?:ة)?)\b/u', $msg, $m)
        ) {
            $state['slots']['shift'] = $m[1];
        }

        // payment
        if (
            empty($state['slots']['payment_method']) &&
            preg_match('/\b(نقدي|بطاقة\s*ائتمان)\b/u', $msg, $m)
        ) {
            $state['slots']['payment_method'] = $m[1];
        }

        // relative / absolute appointment date
        if (empty($state['slots']['appointment_date'])) {
            $date = $this->parseRelativeDate($msg);
            if ($date) {
                $state['slots']['appointment_date'] = $date->format('Y-m-d');
            }
        }
    }

    /**
     * Build a reply listing current offers.
     */
    private function generateOffersReply(): string
    {
        $offers = $this->getOffers();
        if ($offers->isEmpty()) {
            return "عذراً، لا توجد عروض متاحة حالياً 😔.";
        }
        $lines = ["🛍️ هذه بعض العروض الحالية:"];
        foreach ($offers as $o) {
            $lines[] = "- {$o->title} : {$o->price}";
        }
        return implode("\n", $lines);
    }

    /**
     * Build a reply with social / location links.
     */
    private function generateSocialLinksReply(): string
    {
        // assuming you store your Linktree or social link in a settings table:
        $link = DB::table('settings')->where('key', 'linktree')->value('value');
        if (! $link) {
            return "عذراً، لا توجد لدينا روابط حاليا.";
        }
        return "📍 يمكنك التواصل معنا عبر الرابط التالي:\n{$link}";
    }
}
