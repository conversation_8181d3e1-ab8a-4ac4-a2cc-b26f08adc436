<x-app-layout :title="__('Edit Appointment')" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="flex items-center space-x-4 py-5 lg:py-6">
            <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                {{ __('Edit Appointment') }}
            </h2>
            <!-- يمكن إضافة شريط تنقل (Breadcrumb) أو معلومات إضافية هنا -->
        </div>

        <div class="grid grid-cols-12">
            <div class="col-span-12">
                <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
                    <form action="{{ route('appointments.update', $appointment->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        {{-- Patient Name --}}
                        <x-input-text
                            :label="__('Patient Name')"
                            name="full_name"
                            value="{{ old('full_name', $appointment->full_name) }}"
                            placeholder="Enter Full Name"
                            required
                        />

                        {{-- Phone Number --}}
                        <x-input-text
                            :label="__('Phone Number')"
                            name="client_phone"
                            value="{{ old('client_phone', $appointment->client_phone) }}"
                            placeholder="Enter patient phone"
                            required
                        />

                        {{-- National ID --}}
                        <x-input-text
                            :label="__('National ID')"
                            name="identity_number"
                            value="{{ old('identity_number', $appointment->identity_number) }}"
                            placeholder="Enter National ID"
                            required
                        />

                        {{-- Birth Date --}}
                        <x-input-text
                            :label="__('Birth Date')"
                            name="birth_date"
                            value="{{ old('birth_date', $appointment->birth_date) }}"
                            type="date"
                            required
                        />

                        {{-- Clinic --}}
                        <x-select-input
                            :label="__('Clinic')"
                            name="clinic"
                            :options="$clinics->pluck('name', 'name')->toArray()"
                            value="{{ old('clinic', $appointment->clinic) }}"
                            required
                        />

                        {{-- Doctor --}}
                        <x-select-input
                            :label="__('Doctor')"
                            name="doctor_id"
                            :options="$doctors->pluck('name', 'id')->toArray()"
                            value="{{ old('doctor_id', $appointment->doctor_id) }}"
                            required
                        />

                        {{-- Shift --}}
                        <x-input-text
                            :label="__('Shift')"
                            name="shift"
                            value="{{ old('shift', $appointment->shift) }}"
                            placeholder="مثال: 8 ص - 12 م"
                            required
                        />

                        {{-- Payment Method --}}
                        <x-input-text
                            :label="__('Payment Method')"
                            name="payment_method"
                            value="{{ old('payment_method', $appointment->payment_method) }}"
                            placeholder="مثال: كاش, تامين"
                            required
                        />

                        {{-- Appointment Date --}}
                        <x-input-text
                            :label="__('Appointment Date')"
                            name="appointment_date"
                            value="{{ old('appointment_date', $appointment->appointment_date) }}"
                            type="date"
                            required
                        />

                        {{-- Status --}}
                        <x-select-input
                            :label="__('Status')"
                            name="status"
                            :options="['waiting' => 'Waiting', 'assigned' => 'Assigned']"
                            value="{{ old('status', $appointment->status) }}"
                            required
                        />

                        {{-- Waiting Number --}}
                        <x-input-text
                            :label="__('Waiting Number')"
                            name="waiting_number"
                            value="{{ old('waiting_number', $appointment->waiting_number) }}"
                            placeholder="Enter Waiting Number"
                        />

                        <x-save-edit-button route="{{ route('appointments.' . $status) }}" />
                    </form>
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
