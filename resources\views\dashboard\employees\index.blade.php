<x-app-layout :title="__('Employees')" is-header-blur="true">
    <!-- Main Content Wrapper -->
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-4 grid grid-cols-1 gap-4 sm:mt-5 sm:grid-cols-3 sm:gap-5 lg:mt-6 lg:gap-6">
            <!-- Card 1: Total Employees -->
            <div class="card shadow-none">
                <div class="mt-2 flex h-8 items-center justify-between px-4">
                    <h2 class="font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                        {{ __("Total Employees") }}
                    </h2>
                </div>
                <p class="grow px-4">
                    <span class="text-2xl font-semibold text-success">{{ $totalEmployees }}</span>
                </p>
            </div>

            <!-- Cards for Each Role -->
            @foreach ($rolesCount as $roleName => $count)
                <div class="card shadow-none">
                    <div class="mt-2 flex h-8 items-center justify-between px-4">
                        <h2 class="font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                            {{ $roleName }}
                        </h2>
                    </div>
                    <p class="grow px-4">
                        <span class="text-2xl font-semibold text-primary">{{ $count }}</span>
                    </p>
                </div>
            @endforeach
        </div>


        <div class="mt-4 sm:mt-5 lg:mt-6">
            <div class="flex items-center justify-between">
                <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    {{ __("Employees") }}
                </h2>
                <div class="flex">
                    <div class="flex items-center" x-data="{ isInputActive: false }">
                        <label class="block">
                            <input x-effect="isInputActive === true && $nextTick(() => { $el.focus()});"
                                :class="isInputActive ? 'w-32 lg:w-48' : 'w-0'"
                                class="form-input bg-transparent px-1 text-right transition-all duration-100 placeholder:text-slate-500 dark:placeholder:text-navy-200"
                                placeholder="Search here..." type="text" />
                        </label>
                        <button @click="isInputActive = !isInputActive"
                            class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>
                    <div class="inline-flex">
                        <a href="{{ route('employees.create') }}"
                            class="btn space-x-2 bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                            <i class="fas fa-plus"></i>
                            <span>{{ __("Create Employee") }}</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-3 grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-5 lg:grid-cols-3 lg:gap-6">
                @forelse ($employees as $user)
                    <div class="card space-y-4 p-4 sm:p-5">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="avatar">
                                    <img class="mask is-squircle w-12 h-12" src="{{ $user->avatar ? asset("storage/avatars/$user->avatar") : asset('images/200x200.png') }}"
                                        alt="avatar">
                                </div>
                                <div>
                                    <p class="font-medium text-slate-700 dark:text-navy-100">{{ $user->name }}</p>
                                    <p class="text-xs text-slate-400 dark:text-navy-300">
                                        {{ $user->getRoleNames()->first() }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{{ route('employees.edit', $user->id) }}"
                                    class="btn size-8 p-0 text-info hover:bg-info/20 focus:bg-info/20 active:bg-info/25">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('employees.destroy', $user->id) }}" method="POST"
                                    onsubmit="return confirm('هل أنت متأكد من حذف هذا الموظف؟');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                        class="btn size-8 p-0 text-error hover:bg-error/20 focus:bg-error/20 active:bg-error/25">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @empty
                    <p>No employees found.</p>
                @endforelse

            </div>
            <div class="mt-4">
                {{ $employees->links() }}
            </div>
        </div>
    </main>
</x-app-layout>
