<div class="mt-4 grid grid-cols-12 gap-4 bg-slate-150 py-5 dark:bg-navy-800 sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
    <div class="col-span-12 flex flex-col px-[var(--margin-x)] transition-all duration-[.25s] lg:col-span-3 lg:pr-0">
        <h2 class="text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100 lg:text-xl">
            {{ __('Doctors') }}
        </h2>

        <p class="mt-3 grow">
            {{ __('List Of The Doctors and the numbers of appointments') }}
        </p>

        <div class="mt-4">
            <p>{{ __('Total Doctors') }}</p>
            <div class="mt-1.5 flex items-center space-x-2">
                <div class="flex size-7 items-center justify-center rounded-full bg-success/15 text-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 11l5-5m0 0l5 5m-5-5v12" />
                    </svg>
                </div>
                <p class="text-base font-medium text-slate-700 dark:text-navy-100">
                    {{ $doctors->count() }}
                </p>
            </div>
        </div>
    </div>
    <div
        class="is-scrollbar-hidden col-span-12 flex space-x-4 overflow-x-auto px-[var(--margin-x)] transition-all duration-[.25s] lg:col-span-9 lg:pl-0">
        @forelse ($doctors as $doctor)
            <div class="card w-72 shrink-0 space-y-9 rounded-xl p-4 sm:px-5">
                <div class="flex items-center justify-between space-x-2">
                    <div class="flex items-center space-x-3">
                        <div class="avatar">
                            <img class="mask is-squircle" src="{{ asset('images/male_doc.jpg') }}" alt="image" />
                        </div>
                        <div>
                            <p class="font-medium text-slate-700 line-clamp-1 dark:text-navy-100">
                                {{ $doctor->name }}
                            </p>
                            <p class="text-xs text-slate-400 dark:text-navy-300">
                                {{ $doctor->special }}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between space-x-2">
                    <div>
                        <p class="text-xs-plus">{{ __('Total Appoinments') }}</p>
                        <p class="text-xl font-semibold text-slate-700 dark:text-navy-100">
                            {{ $doctor->appointments->count() }}
                        </p>
                    </div>
                </div>
                <div class="grow">
                    <div class="flex w-full space-x-1">
                        <div x-tooltip="''" class="h-2 w-4/12 rounded-full bg-primary dark:bg-accent">
                        </div>
                        <div x-tooltip="''" class="h-2 w-3/12 rounded-full bg-success"></div>
                        <div x-tooltip="''" class="h-2 w-5/12 rounded-full bg-info"></div>
                    </div>
                    <div class="mt-2 flex flex-wrap">
                        <div class="mr-4 mb-1 inline-flex items-center space-x-2 font-inter">
                            <div class="size-2 rounded-full bg-primary dark:bg-accent"></div>
                            <div class="flex space-x-1 text-xs leading-6">
                                <span
                                    class="font-medium text-slate-700 dark:text-navy-100">{{ $doctor->special }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2">
                        <img x-tooltip="'Award Level 1'" class="size-6" src="{{ asset('images/awards/award-19.svg') }}"
                            alt="avatar" />
                        <img x-tooltip="'Award Level 2'" class="size-6" src="{{ asset('images/awards/award-2.svg') }}"
                            alt="avatar" />
                        <img x-tooltip="'Award Level 3'" class="size-6" src="{{ asset('images/awards/award-5.svg') }}"
                            alt="avatar" />
                    </div>
                    <a href="{{ route('doctors.edit', $doctor->id) }}"
                        class="btn -mr-1.5 size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </a>
                </div>
            </div>
        @empty
        @endforelse
    </div>
</div>
