<?php

namespace App\Main;


class SidebarPanel
{
    public static function dashboards()
    {
        return [
            'title' => 'Dashboards',
            'items' => [
                [
                    'dashboards_crm_analytics' => [
                        'title'      => __("CRM Analytics"),
                        'route_name' => 'index',
                        'permission' => 'read dashboards', // Required permission key
                    ],
                    'appointments' => [
                        'title'    => __("Appointments"),
                        'permission' => 'read appointments', // You can set a permission for the main item too
                        'submenu'  => [
                            'waiting_appointments' => [
                                'title'      => __("Waiting Appointments"),
                                'route_name' => 'appointments.waiting',
                                'permission' => 'read appointments',
                            ],
                            'assigned_appointments' => [
                                'title'      => __("Assigned Appointments"),
                                'route_name' => 'appointments.assigned',
                                'permission' => 'read appointments',
                            ]
                        ]
                    ],
                    'clinics' => [
                        'title'      => __("Clinics"),
                        'route_name' => 'clinics.index',
                        'permission' => 'read clinics',
                    ],
                    'offers' => [
                        'title'      => __("Offers"),
                        'route_name' => 'clinic-offers.index',
                        'permission' => 'read offers',
                    ],
                    'doctors' => [
                        'title'      => __("Doctors"),
                        'route_name' => 'doctors.index',
                        'permission' => 'read doctors',
                    ],
                    'employees' => [
                        'title'      => __("Employees"),
                        'route_name' => 'employees.index',
                        'permission' => 'read users', // assuming employees are part of users
                    ],
                    'chat' => [
                        'title'      => __("Chat"),
                        'route_name' => 'chats.index',
                        // Optionally set a permission if needed
                    ],
                    'roles' => [
                        'title'      => __("Roles and Permissions"),
                        'route_name' => 'roles.index',
                        'permission' => 'read roles',
                    ],
                    'logs' => [
                        'title'      => __("System Logs"),
                        'route_name' => 'logs.index',
                        'permission' => 'read logs',
                    ],
                    'whatsapp' => [
                        'title'      => __("WhatsApp Connection Manager"),
                        'route_name' => 'whatsapp-client.index',
                        'permission' => 'read logs',
                    ],
                ]
            ]
        ];
    }

    public static function all()
    {
        return [self::dashboards()];
    }
}
