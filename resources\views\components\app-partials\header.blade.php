<nav class="header print:hidden" x-data="notificationPopper({ userId: @json(auth()->user()->id) })" x-init="initNotifications()">
    <!-- App Header -->
    <div class="header-container relative flex w-full bg-white dark:bg-navy-750 print:hidden">
        <!-- Header Items -->
        <div class="flex w-full items-center justify-between">
            <!-- Left: Sidebar Toggle Button -->
            <div class="size-7">
                <button
                    class="menu-toggle cursor-pointer ml-0.5 flex size-7 flex-col justify-center space-y-1.5 text-primary outline-hidden focus:outline-hidden dark:text-accent-light/80"
                    :class="$store.global.isSidebarExpanded && 'active'"
                    @click="$store.global.isSidebarExpanded = !$store.global.isSidebarExpanded">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>

            <!-- Right: Header <PERSON> -->
            <div class="-mr-1.5 flex items-center space-x-2">
                <!-- Mobile Search Toggle -->
                <button @click="$store.global.isSearchbarActive = !$store.global.isSearchbarActive"
                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25 sm:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5 text-slate-500 dark:text-navy-100"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>

                <!-- Dark Mode Toggle -->
                <button @click="$store.global.isDarkModeEnabled = !$store.global.isDarkModeEnabled"
                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                    <svg x-show="$store.global.isDarkModeEnabled"
                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                        class="size-6 text-amber-400" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M11.75 3.412a.818.818 0 01-.07.917 6.332 6.332 0 00-1.4 3.971c0 3.564 2.98 6.494 6.706 6.494a6.86 6.86 0 002.856-.617.818.818 0 011.1 1.047C19.593 18.614 16.218 21 12.283 21 7.18 21 3 16.973 3 11.956c0-4.563 3.46-8.31 7.925-8.948a.818.818 0 01.826.404z" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" x-show="!$store.global.isDarkModeEnabled"
                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                        class="size-6 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- Notification Button and Dropdown -->
                <div class="relative">
                    <button @click="toggleNotifications" x-ref="notificationButton"
                        class="btn relative size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-5 text-slate-500 dark:text-navy-100"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6 6 0 10-12 0v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                        <span x-show="unreadCount > 0"
                            class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-xs text-white"
                            x-text="unreadCount"></span>
                    </button>
                    <!-- Notification Dropdown -->
                    <div x-show="isOpen" @click.outside="isOpen = false" x-transition
                        class="absolute {{ app()->getLocale() === 'ar' ? 'left' : 'right' }}-0 mt-2 w-80 rounded-md border bg-white shadow-lg dark:border-navy-600 dark:bg-navy-700">
                        <div class="p-4">
                            <div class="flex items-center justify-between">
                                <h3 class="mb-2 text-lg font-semibold text-slate-700 dark:text-navy-100">
                                    {{ __('Notifications') }}
                                </h3>
                                <!-- "Read All" button -->
                                <button @click="markAllAsRead()"
                                    class="btn size-9 p-0 font-medium text-error hover:bg-error/20 focus:bg-error/20 active:bg-error/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                            <template x-if="notifications && notifications.length > 0">
                                <div class="space-y-2">
                                    <template x-for="notification in notifications" :key="notification.id">
                                        <div class="flex items-start space-x-3">
                                            <div
                                                class="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-secondary/10 dark:bg-secondary-light/15">
                                                <i class="fa fa-bell text-secondary dark:text-secondary-light"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-slate-600 dark:text-navy-100"
                                                    x-text="notification.message"></p>
                                                <p class="mt-1 text-xs text-slate-400 dark:text-navy-300"
                                                    x-text="new Date(notification.created_at).toLocaleString()"></p>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </template>
                            <template x-if="notifications && notifications.length === 0">
                                <div class="text-center text-sm text-slate-500 dark:text-navy-100">
                                    {{ __('No new notifications.') }}
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Monochrome Mode Toggle -->
                <button @click="$store.global.isMonochromeModeEnabled = !$store.global.isMonochromeModeEnabled"
                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                    <i
                        class="fa-solid fa-palette bg-linear-to-r from-sky-400 to-blue-600 bg-clip-text text-lg font-semibold text-transparent"></i>
                </button>
                <!-- Language Toggle Button -->
                <a href="{{ route('switch-language') }}"
                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25">
                    <!-- Show "Ar" if the current language is English, or "En" if it's Arabic -->
                    {{ app()->getLocale() === 'en' ? 'Ar' : 'En' }}
                </a>
            </div>
        </div>
    </div>
</nav>

<script>
    function notificationPopper(config = {}) {
        return {
            isOpen: false,
            notifications: [],
            unreadCount: 0,
            userId: config.userId || null,

            toggleNotifications() {
                this.isOpen = !this.isOpen;
            },

            fetchNotifications() {
                fetch('/notifications', {
                        credentials: 'include'
                    })
                    .then(r => r.json())
                    .then(data => {
                        this.notifications = data.notifications || [];
                        this.unreadCount = data.unread_count || 0;
                    })
                    .catch(console.error);
            },

            markAllAsRead() {
                fetch('/notifications/mark-all-read', {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(r => {
                        if (r.ok) {
                            this.unreadCount = 0;
                        } else {
                            console.error("Failed to mark all notifications as read.");
                        }
                    })
                    .catch(console.error);
            },

            initNotifications() {
                // 1) Load existing notifications from DB on init
                this.fetchNotifications();

                // 2) Subscribe to the user's private channel for real-time notifications
                if (window.Echo && this.userId) {
                    window.Echo.private(`App.Models.User.${this.userId}`)
                        .notification((notification) => {
                            console.log("Private notification received:", notification);

                            // Insert the new notification into the list
                            this.notifications.unshift({
                                id: notification.id ?? null,
                                message: notification.message,
                                ticketData: notification.ticketData,
                                created_at: new Date().toISOString()
                            });

                            // Increment unread count
                            this.unreadCount++;

                            // 3) Play a notification sound
                            const audio = new Audio('/sounds/notification.wav');
                            audio.play().catch(err => {
                                console.error("Audio playback failed:", err);
                            });
                        });
                } else {
                    console.error("Echo not loaded or userId missing.");
                }
            }
        }
    }
</script>
