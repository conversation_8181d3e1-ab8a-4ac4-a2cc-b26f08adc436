<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->foreignId('doctor_id')->nullable();
            $table->foreignId('clinic_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->dropColumn('doctor_id');
            $table->dropColumn('clinic_id');
        });
    }
};
