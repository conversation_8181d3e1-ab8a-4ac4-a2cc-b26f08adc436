<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-4 grid grid-cols-1 gap-4 sm:mt-5 sm:grid-cols-3 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="card col-span-12 lg:col-span-12 xl:col-span-12">
                <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                    <div class="mb-6">
                        <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">{{ $title }}</h2>
                    </div>
                    @can('create offers')
                        <div class="mb-4">
                            <a href="{{ route('clinic-offers.create') }}"
                                class="btn space-x-2 bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                                <i class="fas fa-plus"></i>
                                <span>{{ __('Create New Offer') }}</span>
                            </a>
                        </div>
                    @endcan
                </div>
                <div class="card p-4 sm:p-5 shadow-lg rounded-lg overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-200 dark:divide-navy-500 text-center">
                        <thead class="bg-slate-50 dark:bg-navy-700">
                            <tr>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __('Name') }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __('Price') }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __('Description') }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __('Image') }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    <i class="fa fa-bolt" aria-hidden="true"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-200 dark:divide-navy-500">
                            @foreach ($offers as $offer)
                                <tr>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">{{ $offer->title }}</td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">{{ $offer->price }}</td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        {{ mb_substr($offer->description, 0, 90) }}...
                                    </td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        <img src="{{ $offer->image }}" height="40" width="40">
                                    </td>

                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        @can('update offers')
                                            <a href="{{ route('clinic-offers.edit', $offer->id) }}"
                                                class="btn size-8 p-0 text-info hover:bg-info/20 focus:bg-info/20 active:bg-info/25">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endcan
                                        @can('delete offers')
                                            <form action="{{ route('clinic-offers.destroy', $offer->id) }}" method="POST"
                                                class="inline-block ml-2"
                                                onsubmit="return confirm('{{ __('Are you sure you want to delete this offer?') }}');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="btn size-8 p-0 text-error hover:bg-error/20 focus:bg-error/20 active:bg-error/25">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </form>
                                        @endcan
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
