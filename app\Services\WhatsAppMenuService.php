<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\ConversationState;
use App\Models\WaitingTicket;
use App\Models\UserProfile; // ensure you have the model & table

class WhatsAppMenuService
{
    // Timeout in minutes (e.g., 30 means reset to main_menu after 30 mins of inactivity).
    protected $conversationTimeout = 30;

    /**
     * Main entry point for processing user messages.
     *
     * @param string $clientPhone The user's phone number (including country code).
     * @param string $userMessage The text message from the user.
     * @return string The bot's reply to send back.
     */
    public function processUserMessage(string $clientPhone, string $userMessage): string
    {
        // Retrieve or create a conversation state for this user.
        $state = ConversationState::firstOrCreate(
            ['client_phone' => $clientPhone],
            [
                'client_phone' => $clientPhone,
                'current_step' => 'main_menu',
                'temp_data'    => ''
            ]
        );

        // Check if conversation has timed out.
        if ($this->conversationTimedOut($state)) {
            $this->resetState($state);
            return $this->showMainMenu($clientPhone);
        }

        // Route to the appropriate step based on current_step.
        $reply = $this->handleConversation($state, trim($userMessage));

        // Optional: log an analytics event or store usage stats
        // e.g. ChatEvent::create([...]);

        return $reply;
    }

    /**
     * Decides which handler to call based on the conversation state's current_step.
     */
    private function handleConversation(ConversationState $state, string $input): string
    {
        switch ($state->current_step) {
            case 'main_menu':
                return $this->handleMainMenu($state, $input);

            case 'collect_name':
                return $this->handleName($state, $input);

            case 'collect_birth':
                return $this->handleBirth($state, $input);

            case 'collect_phone':
                return $this->handlePhone($state, $input);

            case 'collect_identity':
                return $this->handleIdentity($state, $input);

            case 'collect_clinic':
                return $this->handleClinic($state, $input);

            default:
                // Fallback if state is unknown
                return "عذرًا، حدث خطأ ما. الرجاء إعادة المحاولة.";
        }
    }

    // -----------------------------------------------
    //                  MENU STEPS
    // -----------------------------------------------

    /**
     * 1) Main Menu
     */
    private function handleMainMenu(ConversationState $state, string $input): string
    {
        // If user input is invalid or empty, re-display the menu
        if (empty($input) || !is_numeric($input)) {
            // Example check if user has a profile and offer a different menu
            $profile = UserProfile::where('phone', $state->client_phone)->first();
            if ($profile) {
                // You could show a "reuse profile" menu, for instance
            }
            return $this->showMainMenu($state->client_phone);
        }

        $choice = intval($input);
        switch ($choice) {
            case 1:
                // Move to collecting user data
                $state->current_step = 'collect_name';
                $state->save();
                return "لحجز رقم انتظار اليوم، أرسل اسمك الرباعي:";

            case 2:
                // Lab result logic
                return "للحصول على نتيجة التحاليل، أرسل رقم التحليل أو أي معلومات أخرى...";

            case 3:
                // Connect to support
                return "سيتم تحويلك إلى الدعم... (هنا يمكنك إضافة منطق إضافي)";

            default:
                // Unrecognized menu option
                return "الرجاء اختيار رقم صحيح من القائمة.\n" . $this->showMainMenu($state->client_phone);
        }
    }

    /**
     * 2) Collect Full Name
     */
    private function handleName(ConversationState $state, string $input): string
    {
        // Validate name is at least 4 words
        if (substr_count($input, ' ') < 3) {
            return "الاسم يجب أن يكون رباعياً. الرجاء إعادة الإدخال (مثال: محمد أحمد علي خالد).";
        }

        // Save to temp_data
        $tempData = $this->getTempData($state);
        $tempData['full_name'] = $input;
        $this->setTempData($state, $tempData);

        $state->current_step = 'collect_birth';
        $state->save();
        return "تم استلام الاسم. الآن أرسل تاريخ ميلادك بصيغة يوم-شهر-سنة (مثال: 10-05-1990):";
    }

    /**
     * 3) Collect Birth Date
     */
    private function handleBirth(ConversationState $state, string $input): string
    {
        // Validate date format (DD-MM-YYYY)
        if (!preg_match('/^\d{2}-\d{2}-\d{4}$/', $input)) {
            return "صيغة التاريخ غير صحيحة (يوم-شهر-سنة). مثال: 10-05-1990.\n أعد الإدخال:";
        }

        $tempData = $this->getTempData($state);
        $tempData['birth_date'] = $input;
        $this->setTempData($state, $tempData);

        $state->current_step = 'collect_phone';
        $state->save();
        return "تم استلام تاريخ الميلاد. الآن أرسل رقم جوالك:";
    }

    /**
     * 4) Collect Phone
     */
    private function handlePhone(ConversationState $state, string $input): string
    {
        // Validate phone: numeric + min length
        if (!ctype_digit($input) || strlen($input) < 6) {
            return "رقم الجوال غير صالح. الرجاء إعادة الإدخال:";
        }

        $tempData = $this->getTempData($state);
        $tempData['user_phone'] = $input;
        $this->setTempData($state, $tempData);

        $state->current_step = 'collect_identity';
        $state->save();
        return "تم استلام رقم الجوال. الآن أرسل رقم الهوية:";
    }

    /**
     * 5) Collect Identity Number
     */
    private function handleIdentity(ConversationState $state, string $input): string
    {
        if (!ctype_digit($input) || strlen($input) < 4) {
            return "رقم الهوية غير صالح. الرجاء إعادة الإدخال:";
        }

        $tempData = $this->getTempData($state);
        $tempData['identity_number'] = $input;
        $this->setTempData($state, $tempData);

        $state->current_step = 'collect_clinic';
        $state->save();
        return "تم استلام رقم الهوية. الآن أرسل اسم العيادة المطلوبة:";
    }

    /**
     * 6) Collect Clinic Name & Create Waiting Ticket
     */
    private function handleClinic(ConversationState $state, string $input): string
    {
        $tempData = $this->getTempData($state);
        $tempData['clinic'] = $input;

        // Reformat birth_date if needed
        $birthStr = $tempData['birth_date'] ?? null;
        if ($birthStr) {
            try {
                $parsed = Carbon::createFromFormat('d-m-Y', $birthStr);
                if ($parsed->isFuture()) {
                    // If it's a future date
                    return "تاريخ ميلادك لا يمكن أن يكون في المستقبل. الرجاء إعادة الإدخال.";
                }
                $birthStr = $parsed->format('Y-m-d');
            } catch (\Exception $e) {
                // If parsing fails, set to null or re-prompt
                $birthStr = null;
            }
        }

        // Create waiting ticket in DB
        $ticket = WaitingTicket::create([
            'client_phone'    => $state->client_phone,
            'full_name'       => $tempData['full_name'] ?? '',
            'birth_date'      => $birthStr,
            'identity_number' => $tempData['identity_number'] ?? '',
            'clinic'          => $tempData['clinic'],
            'status'          => 'waiting',
        ]);

        // Also store data in user_profiles so user can reuse it next time
        $this->updateUserProfile($state->client_phone, $tempData, $birthStr);

        // Reset the conversation
        $this->resetState($state);

        return "تم استقبال بياناتك:\n"
            . "الاسم: {$ticket->full_name}\n"
            . "تاريخ الميلاد: {$ticket->birth_date}\n"
            . "رقم الهوية: {$ticket->identity_number}\n"
            . "العيادة: {$ticket->clinic}\n\n"
            . "سيتم إرسال رقم الانتظار حالما يقوم الإداري بتأكيد الطلب.\n\n"
            . "للعودة للقائمة الرئيسية أرسل أي رسالة.";
    }

    // -----------------------------------------------
    //             HELPER METHODS
    // -----------------------------------------------

    /**
     *  Shows the main menu text.
     */
    private function showMainMenu(string $clientPhone = ''): string
    {
        // Example: If you want to detect an existing user profile
        // $profile = UserProfile::where('phone', $clientPhone)->first();
        // if ($profile) {
        //     // Provide a different or advanced menu
        // }

        return "مرحبا في مجمع مردوم الطبي:\n"
            . "1) احجز رقم انتظار اليوم\n"
            . "2) احصل على نتيجة التحاليل\n"
            . "3) تحدث مع الدعم\n"
            . "يرجى إرسال رقم الخيار المطلوب:";
    }

    /**
     * Checks if the conversation has timed out based on updated_at.
     */
    private function conversationTimedOut(ConversationState $state): bool
    {
        return $state->updated_at->diffInMinutes(now()) > $this->conversationTimeout;
    }

    /**
     * Resets the conversation state to main_menu.
     */
    private function resetState(ConversationState $state)
    {
        $state->current_step = 'main_menu';
        $state->temp_data = '';
        $state->save();
    }

    /**
     * Retrieves temp data from the conversation state.
     */
    private function getTempData(ConversationState $state): array
    {
        return json_decode($state->temp_data ?: '{}', true);
    }

    /**
     * Sets temp data back into conversation state.
     */
    private function setTempData(ConversationState $state, array $tempData)
    {
        $state->temp_data = json_encode($tempData, JSON_UNESCAPED_UNICODE);
        $state->save();
    }

    /**
     * Store or update user profile for future usage.
     */
    private function updateUserProfile(string $phone, array $tempData, ?string $birthStr)
    {
        UserProfile::updateOrCreate(
            ['phone' => $phone],
            [
                'full_name'       => $tempData['full_name'] ?? '',
                'birth_date'      => $birthStr,
                'identity_number' => $tempData['identity_number'] ?? '',
            ]
        );
    }
}
