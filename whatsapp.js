// whatsapp.js (ES Module)

import pkg from "whatsapp-web.js";
import QRCode from "qrcode";
import axios from "axios";
import express from "express";
import bodyParser from "body-parser";
import cors from "cors";
import fs from "fs";

const { Client, LocalAuth } = pkg;

// Path to store connection state
const STATE_FILE_PATH = "./connection_state.json";

// Function to read connection state from file
function readConnectionState() {
    try {
        if (fs.existsSync(STATE_FILE_PATH)) {
            const data = fs.readFileSync(STATE_FILE_PATH, "utf8");
            return JSON.parse(data);
        }
    } catch (error) {
        console.error("Error reading connection state:", error);
    }
    return { connected: false };
}

// Function to write connection state to file
function writeConnectionState(state) {
    try {
        fs.writeFileSync(STATE_FILE_PATH, JSON.stringify(state), "utf8");
    } catch (error) {
        console.error("Error writing connection state:", error);
    }
}

// Get initial state
let clientState = readConnectionState();
let lastQR = null;

// Create an Express app
const app = express();
app.use(bodyParser.json());
app.use(cors());

// Initialize the WhatsApp client using LocalAuth to persist sessions
const client = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
        ],
        timeout: 120000, // Increase timeout to 2 minutes
        headless: true,
        defaultViewport: null
    },
});

// When a QR code is received, generate a data URL and store it
client.on("qr", (qr) => {
    console.log("QR RECEIVED, generating data URL...");
    // Update state to not connected when QR code is shown
    clientState.connected = false;
    writeConnectionState(clientState);

    QRCode.toDataURL(qr, { errorCorrectionLevel: "H" }, (err, url) => {
        if (err) {
            console.error("Error generating QR code data URL:", err);
        } else {
            lastQR = url;
            console.log("QR Code Data URL generated and stored.");
        }
    });
});

// When the client is ready, mark it as connected and clear the stored QR code
client.on("ready", () => {
    console.log("WhatsApp client is ready!");
    clientState.connected = true;
    writeConnectionState(clientState);
    lastQR = null;
});

// When the client is disconnected
client.on("disconnected", () => {
    console.log("WhatsApp client disconnected!");
    clientState.connected = false;
    writeConnectionState(clientState);
    lastQR = null;
});

// Endpoint to serve the QR code or connection status
// Modify your /qr endpoint in whatsapp.js
app.get("/qr", async (req, res) => {
    console.log("QR endpoint accessed, state:", clientState.connected, "QR available:", !!lastQR);

    // First check the stored state
    if (clientState.connected) {
        return res.json({ connected: true });
    }
    // Then check if we have a QR code
    if (lastQR) {
        console.log("Returning QR code to client");
        return res.json({ qr: lastQR, connected: false });
    }
    // If neither, we're likely still initializing - return 200 with initializing flag
    console.log("Client still initializing");
    return res.json({
        initializing: true,
        message: "WhatsApp client is initializing. Please try again in a few seconds.",
    });
});

// Endpoint to disconnect the WhatsApp client
app.post("/disconnect", async (req, res) => {
    try {
        await client.logout();
        clientState.connected = false;
        writeConnectionState(clientState);
        lastQR = null;

        // Reinitialize the client to generate a new QR code
        setTimeout(() => {
            client.initialize();
        }, 1000);

        res.json({
            success: true,
            message:
                "Client disconnected successfully. A new QR code will be generated shortly.",
        });
    } catch (error) {
        console.error("Error disconnecting client:", error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Listen for incoming WhatsApp messages
client.on("message", async (message) => {
    console.log(`Message from ${message.from}: ${message.body}`);

    // Skip group messages
    if (message.from.endsWith("@g.us")) {
        console.log("Message is from a group, ignoring.");
        return;
    }

    // Skip empty messages
    if (!message.body || message.body.trim() === "") {
        console.log("Empty message received, ignoring.");
        return;
    }

    // Format the client's phone number (remove '@c.us' and add '+' prefix)
    const clientPhone = "+" + message.from.split("@")[0];

    const requestData = {
        client_phone: clientPhone,
        message: message.body,
    };

    try {
        console.log("Sending message to Laravel API...");
        const laravelApiUrl = "http://localhost:8000/api/whatsapp-message";
        const response = await axios.post(laravelApiUrl, requestData);
        console.log("Response from Laravel:", response.data);

        if (response.data.botReplies && response.data.botReplies.length > 0) {
            for (const reply of response.data.botReplies) {
                if (reply.text) {
                    try {
                        await client.sendMessage(message.from, reply.text);
                        console.log("Message sent successfully");
                    } catch (msgError) {
                        console.error("Error sending specific message:", msgError);
                        // Try an alternative method or handle the specific message error
                    }
                }
            }
        }
    } catch (error) {
        console.error(
            "Error sending message to Laravel:",
            error.response ? error.response.data : error.message,
        );
        await client.sendMessage(
            message.from,
            "There was an error processing your request. Please try again later.",
        );
    }
});

// Optional Express endpoint to send WhatsApp messages (for admin replies)
app.post("/send-message", async (req, res) => {
    let { phone, message, image, caption } = req.body;
    // Remove the "+" if present
    phone = phone.replace(/^\+/, "");
    const chatId = `${phone}@c.us`;

    try {
        let sent;

        if (image) {
            // Send media message with image and caption (if provided)
            sent = await client.sendMessage(chatId, {
                image: { url: image },
                caption: caption || message,
            });
        } else {
            // Send text-only message
            sent = await client.sendMessage(chatId, message);
        }
        console.log("Message sent:", sent);
        res.status(200).json({ success: true, sent });
    } catch (error) {
        console.error("Error sending WhatsApp message:", error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Endpoint to check client status
app.get("/status", async (req, res) => {
    res.json({ connected: clientState.connected });
});

// Start the Express server
const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () =>
  console.log(`WhatsApp service running on port ${PORT}`)
);

// Initialize the WhatsApp client
client.initialize();
