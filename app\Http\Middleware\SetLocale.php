<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $locale = $request->cookie('locale', config('app.locale', 'en'));
        app()->setLocale($locale);

        Log::info('SetLocale middleware running with locale from cookie: ' . $locale);

        return $next($request);
    }
}
