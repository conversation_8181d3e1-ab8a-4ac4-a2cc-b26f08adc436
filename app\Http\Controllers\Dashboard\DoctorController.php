<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Models\Doctor;
use Illuminate\Http\Request;

class DoctorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Doctor $doctor)
    {
        $doctors = $doctor->paginate();
        $title = __("Doctors Management");
        return view('dashboard.doctors.index', compact('doctors', 'title'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $title = __("Create Doctor");
        $clinics = Clinic::get();
        return view('dashboard.doctors.create', compact('title', 'clinics'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validate the incoming request data
        $data = $request->validate([
            'name'     => 'required|string|max:255',
            'clinic_id'  => 'required',
            'schedule' => 'nullable|string|max:255',  // New field for doctor's schedule
        ]);

        Doctor::create($data);
        return redirect()->route('doctors.index')
            ->with('success', __("Doctor created successfully."));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $doctor = Doctor::findOrFail($id);
        $title = __("Edit Doctor");
        $clinics = Clinic::get();
        return view('dashboard.doctors.edit', compact('doctor', 'title', 'clinics'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $doctor = Doctor::findOrFail($id);
        // Validate the incoming request data
        $data = $request->validate([
            'name'     => 'required|string|max:255',
            'clinic_id'  => 'required',
            'schedule' => 'nullable|string|max:255',  // New field for doctor's schedule
        ]);

        $doctor->update($data);
        return redirect()->route('doctors.index')
            ->with('success', __("Doctor updated successfully."));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        Doctor::findOrFail($id)->delete();
        return redirect()->route('doctors.index')
            ->with('success', __("Doctor deleted successfully."));
    }
}
