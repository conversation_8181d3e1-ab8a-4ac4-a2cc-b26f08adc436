<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->string('patient_name');
            $table->string('patient_phone')->nullable();
            $table->string('status')->default('pending');
            $table->dateTime('appointment_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes(); // optional, if you want "deleted_at"
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
