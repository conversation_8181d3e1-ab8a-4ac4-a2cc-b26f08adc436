<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ChatMessage;
use Carbon\Carbon;

class ChatMessagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $clientPhones = [
            '+1234567890',
            '+1234567891',
            '+1234567892',
            '+1234567893',
            '+1234567894',
            '+1234567895',
            '+1234567896',
            '+1234567897',
            '+1234567898',
            '+1234567899',
        ];

        foreach ($clientPhones as $phone) {
            // Create first message from the client
            ChatMessage::create([
                'client_phone' => $phone,
                'message'      => 'Hello, I have an inquiry regarding your services.',
                'sender_type'  => 'client',
                'sender_id'    => null, // client messages may not have a sender_id in your system
                'created_at'   => Carbon::now()->subMinutes(rand(60, 120)),
            ]);

            // Create an admin reply
            ChatMessage::create([
                'client_phone' => $phone,
                'message'      => 'Thank you for reaching out. How can we assist you?',
                'sender_type'  => 'admin',
                'sender_id'    => 1, // assuming the admin has an ID of 1; adjust as needed
                'created_at'   => Carbon::now()->subMinutes(rand(30, 59)),
            ]);

            // Create a follow-up message from the client
            ChatMessage::create([
                'client_phone' => $phone,
                'message'      => 'I need more information on pricing.',
                'sender_type'  => 'client',
                'sender_id'    => null,
                'created_at'   => Carbon::now()->subMinutes(rand(5, 29)),
            ]);
        }
    }
}
