@layer theme, base, components, pages, utilities;

@import "./base.css" layer(base);
@import "./components.css" layer(components);

@import "tailwindcss";

@import "./pages.css" layer(pages);
@import "highlight.js/styles/atom-one-dark.css" layer(components); /* Just for demo purpose only for highlighting code */
@import "./fontawesome-all.css" layer(utilities);

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "Poppins", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-inter: "Inter", sans-serif;
  --color-navy-50: #e7e9ef;
  --color-navy-100: #c2c9d6;
  --color-navy-200: #a3adc2;
  --color-navy-300: #697a9b;
  --color-navy-400: #5c6b8a;
  --color-navy-450: #465675;
  --color-navy-500: #384766;
  --color-navy-600: #313e59;
  --color-navy-700: #26334d;
  --color-navy-750: #222e45;
  --color-navy-800: #202b40;
  --color-navy-900: #192132;

  --color-slate-150: #e9eef5;

  --color-primary: #4f46e5; /* colors.indigo[600] */
  --color-primary-focus: #4338ca; /* colors.indigo[700] */

  --color-secondary-light: #ff57d8;
  --color-secondary: #f000b9;
  --color-secondary-focus: #bd0090;

  --color-accent-light: #818cf8; /* colors.indigo[400] */
  --color-accent: #5f5af6;
  --color-accent-focus: #4d47f5;

  --color-info: #0ea5e9; /* colors.sky[500] */
  --color-info-focus: #0284c7; /* colors.sky[600] */

  --color-success: #10b981; /* colors.emerald[500] */
  --color-success-focus: #059669; /* colors.emerald[600] */

  --color-warning: #ff9800;
  --color-warning-focus: #e68200;

  --color-error: #ff5724;
  --color-error-focus: #f03000;

  --shadow-soft: 0 3px 10px 0 rgb(48 46 56 / 6%);
  --shadow-soft-dark: 0 3px 10px 0 rgb(25 33 50 / 30%);

  --text-tiny: 0.625rem;
  --text-tiny--line-height: 0.8125rem;

  --text-tiny-plus: 0.6875rem;
  --text-tiny-plus--line-height: 0.875rem;

  --text-xs-plus: 0.8125rem;
  --text-xs-plus--line-height: 1.125rem;

  --text-sm-plus: 0.9375rem;
  --text-sm-plus--line-height: 1.375rem;

  --animate-shimmer: shimmer 2s linear infinite;

  @keyframes shimmer {
    from {
      background-position: 0 0;
    }
    to {
      background-position: -200% 0;
    }
  }
}
