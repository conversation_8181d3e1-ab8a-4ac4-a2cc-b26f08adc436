<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AppointmentsExport implements FromView, WithStyles, ShouldAutoSize
{
    protected $appointments;

    public function __construct($appointments)
    {
        $this->appointments = $appointments;
    }

    /**
     * Return a Blade view for the Excel export.
     */
    public function view(): View
    {
        // Pass data to the Blade view
        return view('dashboard.appointments.export', [
            'appointments' => $this->appointments
        ]);
    }

    /**
     * Apply styling to the worksheet after rendering.
     */
    public function styles(Worksheet $sheet)
    {
        // 1) Header row styling
        // Assuming your table has columns A through J
        $headerRange = 'A1:J1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true)->getColor()->setRGB('FFFFFF');
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('3498db');
        $sheet->getStyle($headerRange)->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // 2) Set row height for the header (optional)
        $sheet->getRowDimension(1)->setRowHeight(20);

        // 3) Right-align the data rows (useful for RTL languages)
        // Count how many appointments you have to calculate the last row.
        $lastRow = count($this->appointments) + 1;
        $sheet->getStyle("A2:J{$lastRow}")
              ->getAlignment()
              ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

        // 4) Zebra striping for data rows
        for ($row = 2; $row <= $lastRow; $row++) {
            if ($row % 2 === 0) {
                $sheet->getStyle("A{$row}:J{$row}")
                      ->getFill()
                      ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                      ->getStartColor()
                      ->setRGB('f2f2f2');
            }
        }

        // You can return an array if you prefer, but
        // returning an empty array is also valid.
        return [];
    }
}
