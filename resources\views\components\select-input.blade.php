@props([
    'label' => '',
    'name',
    'options' => [],
    'value' => '',
    'required' => false,
    'id' => $name,
])

<div class="mb-4">
    @if ($label)
        <label for="{{ $id }}" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
            {{ $label }} {!! $required ? '<span class="text-red-500">*</span>' : '' !!}
        </label>
    @endif
    <select id="{{ $id }}" name="{{ $name }}" {{ $required ? 'required' : '' }}
        {{ $attributes->merge([
            'class' =>
                'form-select w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent dark:text-black',
        ]) }}>
        <option value="">{{ __('Select Option') }}</option>
        @foreach ($options as $key => $option)
            <option value="{{ $key }}" {{ old($name, $value) == $key ? 'selected' : '' }}>
                {{ $option }}
            </option>
        @endforeach
    </select>
</div>
