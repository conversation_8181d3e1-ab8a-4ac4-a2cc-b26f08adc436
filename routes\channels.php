<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('chat.{clientPhone}', function ($user, $clientPhone) {
    // Here you can implement your logic to determine if the user can join
    // Only allow authenticated users to listen to the channel
    return (bool) $user;
});

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    // Only authorize if the authenticated user's ID matches the channel ID
    return (int) $user->id === (int) $id;
});
