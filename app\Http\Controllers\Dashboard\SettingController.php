<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function index()
    {
        // Retrieve all settings from DB as [key => value] pairs.
        $settings = Setting::all()->pluck('value', 'key')->toArray();
        $title = __("Settings");
        return view('dashboard.settings.index', compact('title', 'settings'));
    }

    public function update(Request $request)
    {
        // Define the keys you expect in the request.
        // You can add more keys if needed.
        $expectedKeys = [
            'linktree',
        ];

        // Validate the incoming request.
        $request->validate([
            'linktree'  => 'nullable|string',
        ]);

        // Get submitted data for the expected keys.
        $submittedData = $request->only($expectedKeys);

        // Retrieve all current settings keys from the DB.
        $allSettings = Setting::pluck('value', 'key')->toArray();

        // Delete keys that were not submitted or were submitted as an empty string.
        foreach ($allSettings as $key => $value) {
            if (!isset($submittedData[$key]) || trim($submittedData[$key]) === '') {
                Setting::where('key', $key)->delete();
            }
        }

        // Update or create settings for submitted keys that are non-empty.
        foreach ($submittedData as $key => $value) {
            if (trim($value) !== '') {
                Setting::updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }
        }

        return redirect()->back()->with('success', 'Settings updated successfully!');
    }
}
