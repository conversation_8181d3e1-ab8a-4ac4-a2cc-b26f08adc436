<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyBirthDateColumnInWaitingTicketsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration drops the existing `birth_date` column
     * and then recreates it as an unsigned integer column.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            // Drop the existing birth_date column.
            $table->dropColumn('birth_date');
        });

        Schema::table('waiting_tickets', function (Blueprint $table) {
            // Recreate birth_date as an unsigned integer column.
            $table->unsignedInteger('birth_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * This reverts the changes by dropping the integer column
     * and recreating it as a date column.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            // Drop the integer column.
            $table->dropColumn('birth_date');
        });

        Schema::table('waiting_tickets', function (Blueprint $table) {
            // Recreate birth_date as a date column.
            $table->date('birth_date')->nullable();
        });
    }
}
