<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->id();
            // Foreign key linking to waiting_tickets table
            $table->unsignedBigInteger('waiting_ticket_id');
            $table->foreign('waiting_ticket_id')
                ->references('id')
                ->on('waiting_tickets')
                ->onDelete('cascade');

            // Optionally, link to the clinics table if you have one
            $table->unsignedBigInteger('clinic_id')->nullable();
            $table->foreign('clinic_id')
                ->references('id')
                ->on('clinics')
                ->onDelete('set null');

            $table->tinyInteger('rating')->unsigned(); // Rating 1-5
            $table->text('rating_comment')->nullable();
            $table->integer('reminder_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
