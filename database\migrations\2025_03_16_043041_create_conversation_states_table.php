<?php


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConversationStatesTable extends Migration
{
    public function up()
    {
        Schema::create('conversation_states', function (Blueprint $table) {
            $table->string('sender_id')->primary();
            $table->json('state');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('conversation_states');
    }
}
