<x-app-layout :title="__('CRM Analytics')" is-header-blur="true">
    <!-- Main Content Wrapper -->
    <main class="main-content w-full pb-8">
        <div
            class="mt-4 grid grid-cols-12 gap-4 px-[var(--margin-x)] transition-all duration-[.25s] sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            @include('dashboard.crm.overview')
            <div class="card col-span-12 lg:col-span-8">
                <div class="flex items-center justify-between py-3 px-4">
                    <h2 class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                        {{ __('Latest Booked Clinics') }}
                    </h2>
                    <div x-data="usePopper({ placement: 'bottom-end', offset: 4 })" @click.outside="if(isShowPopper) isShowPopper = false"
                        class="inline-flex">
                        <a href="{{ route('appointments.waiting') }}" x-ref="popperRef"
                            @click="isShowPopper = !isShowPopper"
                            class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <i class="fa fa-eye" aria-hidden="true"></i>
                        </a>

                    </div>
                </div>
                <div class="grid grid-cols-1 gap-y-4 pb-3 sm:grid-cols-3">
                    @forelse ($last_booked_clinics as $clinic)
                        <div class="flex flex-col justify-between border-4 border-transparent border-l-info px-4">
                            <div>
                                <p class="text-base font-medium text-slate-600 dark:text-navy-100">
                                    {{ $clinic->clinic }}
                                </p>
                                <div class="badge mt-2 bg-info/10 text-info dark:bg-info/15">
                                    {{ __($clinic->status) }}
                                </div>
                            </div>
                            <div>
                                <div class="mt-8">
                                    <p class="font-inter">
                                        <span
                                            class="text-lg font-medium text-slate-600 dark:text-navy-100">{{ $clinic->full_name }}</span>
                                    </p>
                                    <p class="mt-1 text-xs"><i class="fa fa-phone" aria-hidden="true"></i>
                                        {{ $clinic->client_phone }}</p>
                                </div>
                                <div class="mt-8 flex items-center justify-between space-x-2">
                                    <div class="flex -space-x-3">
                                        <div class="p-1 hover:z-10">
                                            <div
                                                class="is-initial rounded-full bg-info text-xs-plus p-2 uppercase text-white ring-3 ring-white dark:ring-navy-700">
                                                {{ $clinic->birth_date }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                    @endforelse
                </div>
            </div>
            <div class="col-span-12 lg:col-span-4">
                <div class="flex items-center justify-between">
                    <h2 class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                        {{ __('Customer Satisfaction') }}
                    </h2>
                </div>
                <div class="mt-3">
                    <p>
                        <span
                            class="text-3xl text-slate-700 dark:text-navy-100">{{ number_format($averageRating, 1) }}</span>
                        <span class="text-xs text-success">({{ $totalRatings }} {{ __('ratings') }})</span>
                    </p>
                    <p class="text-xs-plus">{{ __('Rating Average') }}</p>
                </div>
                <div class="mt-4 flex h-2 space-x-1">
                    <div class="rounded-full bg-primary dark:bg-accent"
                        style="width: {{ $ratingsPercentages[5] ?? 0 }}%;" x-tooltip.primary="'ممتاز'">
                    </div>
                    <div class="rounded-full bg-success" style="width: {{ $ratingsPercentages[4] ?? 0 }}%;"
                        x-tooltip.success="'جيد جداً'">
                    </div>
                    <div class="rounded-full bg-info" style="width: {{ $ratingsPercentages[3] ?? 0 }}%;"
                        x-tooltip.info="'جيد'">
                    </div>
                    <div class="rounded-full bg-warning" style="width: {{ $ratingsPercentages[2] ?? 0 }}%;"
                        x-tooltip.warning="'ضعيف'">
                    </div>
                    <div class="rounded-full bg-error" style="width: {{ $ratingsPercentages[1] ?? 0 }}%;"
                        x-tooltip.error="'سيء جداً'">
                    </div>

                </div>

                <div class="is-scrollbar-hidden mt-4 min-w-full overflow-x-auto">
                    <table class="w-full font-inter">
                        <tbody>
                            <tr>
                                <td class="whitespace-nowrap py-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="size-3.5 rounded-full border-2 border-primary dark:border-accent">
                                        </div>
                                        <p class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                            {{ __('Exellent') }}
                                        </p>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        {{ $ratingsGrouped[5] ?? 0 }}
                                    </p>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">{{ $ratingsPercentages[5] ?? 0 }}%</td>
                            </tr>
                            <tr>
                                <td class="whitespace-nowrap py-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="size-3.5 rounded-full border-2 border-success"></div>
                                        <p class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                            {{ __('Very Good') }}
                                        </p>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        {{ $ratingsGrouped[4] ?? 0 }}
                                    </p>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">{{ $ratingsPercentages[4] ?? 0 }}%</td>
                            </tr>
                            <tr>
                                <td class="whitespace-nowrap py-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="size-3.5 rounded-full border-2 border-info"></div>
                                        <p class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                            {{ __('Good') }}
                                        </p>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        {{ $ratingsGrouped[3] ?? 0 }}
                                    </p>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">{{ $ratingsPercentages[3] ?? 0 }}%</td>
                            </tr>
                            <tr>
                                <td class="whitespace-nowrap py-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="size-3.5 rounded-full border-2 border-warning"></div>
                                        <p class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                            {{ __('Poor') }}
                                        </p>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        {{ $ratingsGrouped[2] ?? 0 }}
                                    </p>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">{{ $ratingsPercentages[2] ?? 0 }}%</td>
                            </tr>
                            <tr>
                                <td class="whitespace-nowrap py-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="size-3.5 rounded-full border-2 border-error"></div>
                                        <p class="font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                            {{ __('Very Poor') }}
                                        </p>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">
                                    <p class="font-medium text-slate-700 dark:text-navy-100">
                                        {{ $ratingsGrouped[1] ?? 0 }}
                                    </p>
                                </td>
                                <td class="whitespace-nowrap py-2 text-right">{{ $ratingsPercentages[1] ?? 0 }}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @include('dashboard.crm.doctors')
    </main>
</x-app-layout>
