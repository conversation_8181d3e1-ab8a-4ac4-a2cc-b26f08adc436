<?php

namespace App\Events;

use App\Models\ChatMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ChatMessageSent implements ShouldBroadcast
{
    use SerializesModels;

    public $chatMessage;

    /**
     * Create a new event instance.
     */
    public function __construct(ChatMessage $chatMessage)
    {
        $this->chatMessage = $chatMessage;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn()
    {
        // We'll use a channel per client conversation.
        $sanitizedPhone = str_replace('+', '', $this->chatMessage->client_phone);
        return new Channel('chat.'.$sanitizedPhone);
    }

    /**
     * Optional: Customize the broadcast name.
     */
    public function broadcastAs()
    {
        return 'ChatMessageSent';
    }
}
