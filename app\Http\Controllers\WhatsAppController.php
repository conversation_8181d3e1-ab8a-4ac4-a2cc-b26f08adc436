<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http; // Laravel's HTTP client
use App\Models\ChatMessage;
use App\Models\WaitingTicket;
use App\Models\Rating;
use App\Models\User;
use App\Notifications\NewRatingNotification;
use App\Notifications\UserHandoffRequested;
use App\Services\ChatService;
use Illuminate\Support\Facades\Notification;

class WhatsAppController extends Controller
{
    protected $chatService;

    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Main entry point for incoming WhatsApp messages.
     */
    public function store(Request $request)
    {
        // 1. Validate incoming request
        $data = $request->validate([
            'client_phone' => 'required|string',
            'message'      => 'required|string',
        ]);

        // Normalize phone number (remove leading +)
        $clientPhone = ltrim($data['client_phone'], '+');
        $userMessage = trim($data['message']);

        Log::info("WhatsApp message from {$clientPhone}: {$userMessage}");

        // 0️⃣  Check for “talk to human” trigger
        if (preg_match('/\b(إنسان|مساعد\s+بشري|admin|support|مشرف)\b/ui', $userMessage)) {
            // flag state
            $state = $this->chatService->getState($clientPhone);
            $state['handoff'] = true;
            $this->chatService->saveState($clientPhone, $state);

            // send to each admin on their private channel
            $admins = User::all();
            foreach ($admins as $admin) {
                Notification::send($admin, new UserHandoffRequested($clientPhone, $admin->id));
            }

            $botReply = "حاضر، سأقوم الآن بربطك بأحد موظفينا البشر 😊";
            // log & persist...
            $this->chatService->updateConversation($clientPhone, 'Bot', $botReply, $state);
            ChatMessage::create([
                'client_phone' => $clientPhone,
                'sender_type'  => 'bot',
                'message'      => $botReply,
            ]);

            return response()->json([
                'status'     => 'success',
                'botReplies' => [['text' => $botReply]],
            ], 200);
        }
        // 2. Log the client's message (optional DB logging of raw chat)
        ChatMessage::create([
            'client_phone' => $clientPhone,
            'sender_type'  => 'client',
            'message'      => $userMessage,
        ]);

        // 3. Check if message is a rating (1-5), handle that separately
        if ($this->isRatingMessage($userMessage)) {
            return $this->handleRatingResponse($clientPhone, $userMessage);
        }

        // 4. Retrieve or initialize conversation state
        $state = $this->chatService->getState($clientPhone);

        // 5. Check if conversation timed out, if yes, re-init state
        if ($this->chatService->conversationTimedOut($state)) {
            $state = $this->chatService->initializeConversationState();
            $state['client_phone'] = $clientPhone;
        }

        // 6. Update conversation with the user’s message in the log
        $this->chatService->updateConversation($clientPhone, 'User', $userMessage, $state);

        // 7. Generate a bot reply via your advanced ChatService method
        $botReply = $this->chatService->generateSmartResponse($state, $userMessage, $clientPhone);

        // 8. Update conversation again for the bot’s reply
        $this->chatService->updateConversation($clientPhone, 'Bot', $botReply, $state);

        // 9. Save updated state
        $this->chatService->saveState($clientPhone, $state);

        // 10. Save bot reply to ChatMessage log (optional)
        ChatMessage::create([
            'client_phone' => $clientPhone,
            'sender_type'  => 'bot',
            'message'      => $botReply,
        ]);

        // 11. Return JSON response
        //   If you want to handle images or other media, you'd handle that here as well.
        return response()->json([
            'status'     => 'success',
            'botReplies' => [
                ['text' => $botReply]
            ],
        ], 200);
    }

    /**
     * Determine if the incoming message is a rating (number between 1 and 5).
     */
    protected function isRatingMessage(string $message): bool
    {
        return (bool) preg_match('/^\s*([1-5])\s*$/', $message);
    }

    /**
     * Handle rating responses by updating or creating a rating record.
     */
    protected function handleRatingResponse(string $clientPhone, string $message)
    {
        preg_match('/^\s*([1-5])\s*$/', $message, $matches);
        $ratingValue = intval($matches[1]);
        Log::info("Detected rating response: {$ratingValue} from {$clientPhone}");

        // Find the most recent assigned waiting ticket for this client.
        $ticket = WaitingTicket::where('client_phone', $clientPhone)
            ->where('status', 'assigned')
            ->latest()
            ->first();

        if (!$ticket) {
            Log::warning("No assigned booking found for rating response from {$clientPhone}");
            return response()->json([
                'status'  => 'error',
                'message' => 'لم يتم العثور على حجز مناسب لتسجيل التقييم.'
            ], 404);
        }

        // Check if a rating already exists for this waiting ticket.
        if ($ticket->rating) {
            // Update the existing rating.
            $ticket->rating->update([
                'clinic_id' => $ticket->clinic_id ?? null,
                'rating'    => $ratingValue,
            ]);
            $action = 'updated';
        } else {
            // Create a new rating record.
            $rating = Rating::create([
                'waiting_ticket_id' => $ticket->id,
                'clinic_id'         => $ticket->clinic_id ?? null,
                'rating'            => $ratingValue,
                'reminder_count'    => 0,
            ]);
            $action = 'created';
        }

        Log::info("Rating {$action} for ticket ID {$ticket->id} with rating {$ratingValue}");

        // Notify admin users (adjust query as needed)
        $adminUsers = \App\Models\User::get();
        Notification::send($adminUsers, new NewRatingNotification($ticket, $ratingValue));

        // Send follow-up based on the rating value.
        if ($ratingValue >= 4) {
            $this->sendFollowUpForHighRating($clientPhone);
            $responseMessage = "شكراً لك على تقييمك الإيجابي 😊! رأيك مهم جداً لتحسين خدماتنا.";
        } else {
            $this->sendFollowUpForLowRating($clientPhone);
            $responseMessage = "شكراً لتقييمك! نحن نأسف إذا لم نلبِّ توقعاتك 😔. نرجو منك مشاركة ملاحظاتك حتى نتمكن من تحسين خدماتنا. 🙏";
        }

        return response()->json([
            'status'  => 'success',
            'message' => $responseMessage
        ], 200);
    }

    /**
     * Send a follow-up message for high ratings (4 or 5).
     */
    protected function sendFollowUpForHighRating(string $clientPhone)
    {
        $googleMapsLink = "https://goo.gl/maps/your-clinic-review-link"; // Replace with your actual link
        $followUpMessage = "شكراً لك على تقييمك الإيجابي 😊! يمكنك تقييم العيادة على خرائط جوجل عبر الرابط: $googleMapsLink";

        $response = Http::post('http://localhost:3000/send-message', [
            'phone'   => $clientPhone,
            'message' => $followUpMessage,
        ]);
        Log::info("High-rating follow-up sent to {$clientPhone}. Response: " . $response->body());
    }

    /**
     * Send a follow-up message for low ratings (3 or below).
     */
    protected function sendFollowUpForLowRating(string $clientPhone)
    {
        $followUpMessage = "نأسف لأن تجربتك لم تكن مرضية تماماً 😔. نرجو منك مشاركة ملاحظاتك حتى نتمكن من تحسين خدماتنا.";
        $response = Http::post('http://localhost:3000/send-message', [
            'phone'   => $clientPhone,
            'message' => $followUpMessage,
        ]);
        Log::info("Low-rating follow-up sent to {$clientPhone}. Response: " . $response->body());
    }
}
