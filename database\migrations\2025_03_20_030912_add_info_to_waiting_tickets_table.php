<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->string('doctor')->nullable();
            $table->string('appointment_date')->nullable();
            $table->string('shift')->nullable();
            $table->string('payment_method')->nullable();
            $table->dropColumn('birth_date');
            $table->date("birth_date")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->dropColumn('doctor');
            $table->dropColumn('appointment_date');
            $table->dropColumn('shift');
            $table->dropColumn('payment_method');
            $table->dropColumn('birth_date');
        });
    }
};
