<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ConversationLog extends Model
{
    protected $table = 'conversation_logs';

    // We disable the default timestamps since we manage our own "timestamp" column.
    public $timestamps = false;

    // Allow mass assignment for these fields.
    protected $fillable = [
        'sender_id',
        'message',
        'speaker',
        'timestamp',
    ];
}
