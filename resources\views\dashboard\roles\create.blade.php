<x-app-layout title="Create Role" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        @can('create roles')
            <div class="flex items-center justify-between py-5 lg:py-6">
                <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">Create Role</h2>
            </div>
        @endcan

        <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
            <form action="{{ route('roles.store') }}" method="POST">
                @csrf
                <x-input-text label="Role Name" name="name" placeholder="Enter role name" required />
                <div class="mb-4">
                    <label class="block text-lg font-medium text-slate-700 dark:text-navy-100 mb-2">Permissions</label>
                    <div class="grid grid-cols-2 gap-2">
                        @foreach ($permissions as $permission)
                            <label class="inline-flex items-center space-x-2">
                                <input
                                    class="form-checkbox is-basic size-5 rounded-sm bg-slate-100 border-slate-400/70 checked:bg-primary checked:border-primary hover:border-primary focus:border-primary dark:bg-navy-900 dark:border-navy-500 dark:checked:bg-accent dark:checked:border-accent dark:hover:border-accent dark:focus:border-accent"
                                    type="checkbox" name="permissions[]" value="{{ $permission->name }}" />
                                <p>{{ $permission->name }}</p>
                            </label>
                        @endforeach
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <a href="{{ route('roles.index') }}"
                        class="btn size-9 rounded-full bg-primary p-0 font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <button type="submit"
                        class="btn space-x-2 bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                        <i class="fas fa-plus"></i>
                        <span>Create Role</span>
                    </button>
                </div>
            </form>
        </div>
    </main>
</x-app-layout>
