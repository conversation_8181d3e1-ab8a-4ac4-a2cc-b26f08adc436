<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WaitingTicket;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SendRatingReminders extends Command
{
    protected $signature = 'send:rating-reminders';
    protected $description = 'Send WhatsApp rating reminders for bookings assigned over 24 hours ago and not yet rated.';

    public function handle()
    {
        $maxReminders = 2;
        $now = Carbon::now();

        // Retrieve waiting tickets that are assigned, have no related rating, and have reminder count < $maxReminders.
        $tickets = WaitingTicket::where('status', 'assigned')
            ->whereDoesntHave('rating')
            ->where('rating_reminder_count', '<', $maxReminders)
            ->get();
        foreach ($tickets as $ticket) {
            $hoursPassed = abs($now->diffInHours(Carbon::parse($ticket->created_at), false));
            Log::info($hoursPassed);
            if ($hoursPassed >= ($ticket->rating_reminder_count + 1) * 24) {
                $message = "مرحباً، نأمل أن تكون تجربتك مع عيادات المختار الطبية قد لبت توقعاتك. نود معرفة رأيك حول جودة الخدمة، هل يمكنك تقييمها من 1 إلى 5؟ رأيك مهم جداً لتحسين خدماتنا. شكراً لك!";

                $response = Http::post('http://localhost:3000/send-message', [
                    'phone'   => $ticket->client_phone,
                    'message' => $message,
                ]);

                if ($response->successful()) {
                    $ticket->rating_reminder_count += 1;
                    $ticket->save();
                    $this->info("Rating reminder sent to {$ticket->client_phone}. Reminder count is now {$ticket->rating_reminder_count}.");
                } else {
                    $this->error("Failed to send reminder to {$ticket->client_phone}. Response: " . $response->body());
                }
            }
        }

        return 0;
    }
}
