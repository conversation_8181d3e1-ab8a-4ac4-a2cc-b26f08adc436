<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <!-- Header -->
        <div class="flex items-center justify-between py-5 lg:py-6">
            <h2 class="text-2xl font-medium text-slate-800 dark:text-navy-50">
                {{ $title }}
            </h2>
        </div>
        <!-- Form Card -->
        <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
            <form action="{{ route('employees.update', $user->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Name Field -->
                <x-input-text
                    :label="__('Name')"
                    name="name"
                    value="{{ old('name', $user->name) }}"
                    placeholder="Enter name"
                    required />

                <!-- Email Field -->
                <x-input-text
                    :label="__('Email')"
                    name="email"
                    value="{{ old('email', $user->email) }}"
                    placeholder="Enter email"
                    required />

                <!-- Optional: Password Update -->
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
                        {{ __("Password") }} ({{ __("Leave blank to keep current") }})
                    </label>
                    <input type="password" id="password" name="password"
                        class="form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent">
                </div>
                <div class="mb-4">
                    <label for="password_confirmation" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
                        {{ __("Confirm Password") }}
                    </label>
                    <input type="password" id="password_confirmation" name="password_confirmation"
                        class="form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent">
                </div>

                <!-- Role Field -->
                <x-select-input
                    :label="__('Role')"
                    name="role"
                    :options="$roles->pluck('name', 'name')->toArray()"
                    value="{{ old('role', $user->getRoleNames()->first()) }}"
                    required />

                <!-- Avatar Upload (Optional) -->
                <div class="mb-4">
                    <label for="avatar" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
                        {{ __("Avatar") }}
                    </label>
                    <input type="file" id="avatar" name="avatar"
                        class="form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent">
                </div>
                <x-save-edit-button route="{{ route('employees.index') }}" />
            </form>
        </div>
    </main>
</x-app-layout>
