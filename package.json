{"name": "lineone", "version": "2.1.1", "description": "Multipurpose Admin UI Kit based on Tailwind CSS", "private": true, "author": "PiniaStudio", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "whatsapp": "node whatsapp.js"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.0", "axios": "^1.7.9", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "tailwindcss": "^4.0.0", "vite": "^6.1.0"}, "dependencies": {"@alpinejs/collapse": "^3.14.8", "@alpinejs/intersect": "^3.14.8", "@alpinejs/persist": "^3.14.8", "@caneara/iodine": "^8.5.0", "@fortawesome/fontawesome-free": "^6.7.2", "@popperjs/core": "^2.11.8", "alpinejs": "^3.14.8", "apexcharts": "^4.4.0", "body-parser": "^1.20.3", "cleave.js": "^1.6.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "express": "^4.21.2", "filepond": "^4.32.7", "filepond-plugin-image-preview": "^4.6.12", "flatpickr": "^4.6.13", "gridjs": "^6.2.0", "highlight.js": "^11.11.1", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "quill": "^2.0.3", "simplebar": "^6.3.0", "sortablejs": "^1.15.6", "swiper": "^11.2.2", "tippy.js": "^6.3.7", "toastify-js": "^1.12.0", "tom-select": "^2.4.2", "whatsapp-web.js": "^1.23.0"}}