<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of employees (users).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $title = __("Employees");
        // Total employees in the system.
        $totalEmployees = User::count();
        $totalEmployees = User::count();
        $growthPercentage = 5; // Example value; replace with your calculated percentage.
        $newHires = User::where('created_at', '>=', now()->subMonth())->count();
        // For growth calculation, compare with employees created before one month ago.
        $previousEmployees = User::where('created_at', '<', now()->subMonth())->count();
        $growthPercentage = $previousEmployees > 0
            ? round((($totalEmployees - $previousEmployees) / $previousEmployees) * 100, 2)
            : ($totalEmployees > 0 ? 100 : 0);

        // New hires for the current month.
        $currentNewHires = User::whereBetween('created_at', [now()->startOfMonth(), now()])->count();
        // New hires for the previous month.
        $previousNewHires = User::whereBetween('created_at', [
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth()
        ])->count();

        $newHiresGrowth = $previousNewHires > 0
            ? round((($currentNewHires - $previousNewHires) / $previousNewHires) * 100, 2)
            : ($currentNewHires > 0 ? 100 : 0);

        $employees = User::orderBy('id', 'desc')->paginate(12);
        $roles = \Spatie\Permission\Models\Role::all();
        $rolesCount = [];
        foreach ($roles as $role) {
            $rolesCount[$role->name] = User::role($role->name)->count();
        }

        return view('dashboard.employees.index', compact(
            'employees',
            'totalEmployees',
            'growthPercentage',
            'currentNewHires',
            'newHiresGrowth',
            'rolesCount',
            'newHires',
            'title'
        ));
    }


    /**
     * Show the form for creating a new employee.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Retrieve all available roles for assignment.
        $roles = Role::all();
        $title = __("Create Employee");
        return view('dashboard.employees.create', compact('roles', 'title'));
    }

    /**
     * Store a newly created employee (user) in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate incoming request data.
        $data = $request->validate([
            'name'     => 'required|string|max:255',
            'email'    => 'required|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'role'     => 'required|string',
            'avatar'   => 'nullable|image|max:2048',
        ]);

        // Create the new user.
        $user = User::create([
            'name'     => $data['name'],
            'email'    => $data['email'],
            'password' => bcrypt($data['password']),
        ]);

        // If an avatar is uploaded, rename it and store only the filename.
        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $extension = $file->getClientOriginalExtension();
            // Create a unique filename using the current timestamp and the user ID.
            $filename = time() . '_' . $user->id . '.' . $extension;
            // Store the file in the "avatars" directory on the public disk.
            $file->storeAs('avatars', $filename, 'public');
            // Save only the filename in the user record.
            $user->avatar = $filename;
            $user->save();
        }

        // Assign the selected role to the user.
        $user->assignRole($data['role']);

        return redirect()->route('employees.index')
            ->with('success', __('Employee created successfully.'));
    }

    /**
     * Show the form for editing the specified employee.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $user = User::findOrFail($id);
        $roles = Role::all();
        $title = __("Edit Employee");
        return view('dashboard.employees.edit', compact('user', 'roles', 'title'));
    }

    /**
     * Update the specified employee (user) in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Retrieve the user to update.
        $user = User::findOrFail($id);

        // Validate the request data.
        $data = $request->validate([
            'name'   => 'required|string|max:255',
            'email'  => 'required|email|max:255|unique:users,email,' . $user->id,
            'role'   => 'required|string',
            // Optional: if updating password
            'password' => 'nullable|string|min:6|confirmed',
            'avatar'   => 'nullable|image|max:2048',
        ]);

        // Prepare data to update.
        $updateData = [
            'name'  => $data['name'],
            'email' => $data['email'],
        ];

        // If a new password is provided, update it.
        if (!empty($data['password'])) {
            $updateData['password'] = bcrypt($data['password']);
        }

        // Update the user's basic info.
        $user->update($updateData);

        // Handle avatar update if a new file is uploaded.
        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $extension = $file->getClientOriginalExtension();
            // Create a unique filename using the current timestamp and user ID.
            $filename = time() . '_' . $user->id . '.' . $extension;
            // Store the file in the "avatars" directory on the public disk.
            $file->storeAs('avatars', $filename, 'public');
            // Save only the filename.
            $user->avatar = $filename;
            $user->save();
        }

        // Synchronize the assigned role (assuming a single role per user).
        $user->syncRoles($data['role']);

        return redirect()->route('employees.index')
            ->with('success', __("Employee updated successfully."));
    }

    /**
     * Remove the specified employee from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return redirect()->route('employees.index')
            ->with('success', __("Employee deleted successfully."));
    }
}
