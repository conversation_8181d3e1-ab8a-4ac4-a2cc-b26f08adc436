<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewWaitingTicketNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $ticketData;
    public $userId;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $ticketData, int $userId)
    {
        $this->ticketData = $ticketData;
        $this->userId     = $userId;
    }

    /**
     * Define which channels to use (broadcast + database, for example).
     */
    public function via($notifiable)
    {
        return ['broadcast', 'database'];
    }

    /**
     * Store in DB (optional).
     */
    public function toDatabase($notifiable)
    {
        return [
            'message'    => "New waiting ticket created for {$this->ticketData['full_name']}!",
            'ticketData' => $this->ticketData,
        ];
    }

    /**
     * Broadcast data (what the frontend receives).
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'message'    => "New waiting ticket created for {$this->ticketData['full_name']}!",
            'ticketData' => $this->ticketData,
        ]);
    }

    /**
     * Broadcast on a private channel named "App.Models.User.{userId}".
     */
    public function broadcastOn()
    {
        return [new PrivateChannel("App.Models.User.{$this->userId}")];
    }
}
