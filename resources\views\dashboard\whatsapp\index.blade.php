<x-app-layout title="Connect WhatsApp" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-4 grid grid-cols-1 gap-4 sm:mt-5 sm:grid-cols-3 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="card col-span-12 lg:col-span-12 xl:col-span-12">
                <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                    <div class="mb-6">
                        <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">{{ __('Connect WhatsApp') }}
                        </h2>
                    </div>
                </div>
                <div class="card p-4 sm:p-5 shadow-lg rounded-lg overflow-x-auto">

                    <div class="qr-container">
                        <p id="status-message">{{ __('Loading WhatsApp connection status...') }}</p>
                        <div id="qr-container" style="display: none;">
                            <p>{{ __('Please scan this QR code with your WhatsApp app:') }}</p>
                            <img id="qr-code" src="" alt="WhatsApp QR Code" style="max-width: 300px;">
                        </div>
                        <div id="connected-container" style="display: none;">
                            <p class="text-green-500">{{ __('WhatsApp is connected and ready to use!') }}</p>
                            <button id="disconnect-btn" type="button"
                                class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded mt-4 btn">
                                {{ __('Disconnect WhatsApp') }}
                            </button>
                        </div>
                        <div id="loading-container">
                            <p>{{ __('Initializing WhatsApp client...') }}</p>
                            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mt-4">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script>
        const API_URL = "http://127.0.0.1:3000";
        let pollingInterval;

        async function checkWhatsAppStatus() {
            try {
                console.log("Checking WhatsApp status...");
                document.getElementById('status-message').textContent = "{{ __('Checking WhatsApp status...') }}";

                const response = await fetch(`${API_URL}/qr`);
                console.log(`Server returned status ${response.status}`);

                const data = await response.json();
                console.log("Response data:", data);

                // Hide loading indicator
                document.getElementById('loading-container').style.display = 'none';

                if (data.connected) {
                    // WhatsApp is connected
                    document.getElementById('qr-container').style.display = 'none';
                    document.getElementById('connected-container').style.display = 'block';
                    document.getElementById('status-message').textContent = "{{ __('WhatsApp client is connected.') }}";
                    resetPolling(10000); // Poll every 10 seconds when connected
                } else if (data.qr) {
                    // QR code is available
                    console.log("QR code received, updating image...");
                    document.getElementById('qr-code').src = data.qr;
                    document.getElementById('qr-container').style.display = 'block';
                    document.getElementById('connected-container').style.display = 'none';
                    document.getElementById('status-message').textContent = "{{ __('Connect your WhatsApp account') }}";
                    resetPolling(3000); // Poll every 3 seconds when showing QR
                } else if (data.initializing) {
                    // Client is initializing
                    document.getElementById('qr-container').style.display = 'none';
                    document.getElementById('connected-container').style.display = 'none';
                    document.getElementById('loading-container').style.display = 'block';
                    document.getElementById('status-message').textContent = data.message ||
                        "{{ __('Initializing WhatsApp client...') }}";
                    resetPolling(2000); // Poll every 2 seconds during initialization
                } else {
                    // Unexpected response
                    console.error("Unexpected response data:", data);
                    document.getElementById('status-message').textContent =
                        "{{ __('Unexpected response from server. Retrying...') }}";
                    document.getElementById('loading-container').style.display = 'block';
                    resetPolling(3000);
                }
            } catch (error) {
                console.error("Error checking WhatsApp status:", error);
                document.getElementById('status-message').textContent =
                    "{{ __('Error checking WhatsApp status. Retrying...') }}";
                document.getElementById('loading-container').style.display = 'block';
                document.getElementById('qr-container').style.display = 'none';
                document.getElementById('connected-container').style.display = 'none';
                resetPolling(5000); // Back off a bit on error
            }
        }

        // Reset polling interval with new time
        function resetPolling(interval) {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
            pollingInterval = setInterval(checkWhatsAppStatus, interval);
        }

        // Disconnect button click handler
        async function disconnectClient() {
            try {
                // Show loading state
                document.getElementById('status-message').textContent = "{{ __('Disconnecting...') }}";
                document.getElementById('disconnect-btn').disabled = true;
                document.getElementById('disconnect-btn').classList.add('opacity-50');
                document.getElementById('loading-container').style.display = 'block';
                document.getElementById('connected-container').style.display = 'none';

                const response = await fetch(`${API_URL}/disconnect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('status-message').textContent = data.message;
                    // Reset polling to check more frequently for the new QR code
                    resetPolling(2000);
                } else {
                    document.getElementById('status-message').textContent =
                        "{{ __('Failed to disconnect. Please try again.') }}";
                    document.getElementById('connected-container').style.display = 'block';
                    document.getElementById('loading-container').style.display = 'none';
                }

                document.getElementById('disconnect-btn').disabled = false;
                document.getElementById('disconnect-btn').classList.remove('opacity-50');
            } catch (error) {
                console.error("Error disconnecting client:", error);
                document.getElementById('status-message').textContent =
                    "{{ __('Error disconnecting client. Please try again.') }}";
                document.getElementById('disconnect-btn').disabled = false;
                document.getElementById('disconnect-btn').classList.remove('opacity-50');
                document.getElementById('connected-container').style.display = 'block';
                document.getElementById('loading-container').style.display = 'none';
            }
        }

        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('disconnect-btn').addEventListener('click', disconnectClient);

            // Initial check
            checkWhatsAppStatus();

            // Start polling (default to 3 seconds)
            resetPolling(3000);
        });

        // Clean up when leaving the page
        window.addEventListener('beforeunload', function() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        });
    </script>
</x-app-layout>
