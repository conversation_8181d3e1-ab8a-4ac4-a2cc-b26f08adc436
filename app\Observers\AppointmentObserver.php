<?php

namespace App\Observers;

use App\Models\Appointment;
use App\Models\Log;

class AppointmentObserver
{
    public function created(Appointment $appointment)
    {
        Log::create([
            'user_id' => auth()->id() ?? null,
            'action_type' => 'appointment.created',
            'old_values' => null,
            'new_values' => json_encode($appointment->toArray()),
        ]);
    }

    public function updated(Appointment $appointment)
    {
        $original = $appointment->getOriginal();
        $changes  = $appointment->getDirty();

        Log::create([
            'user_id' => auth()->id() ?? null,
            'action_type' => 'appointment.updated',
            'old_values'  => json_encode($original),
            'new_values'  => json_encode($changes),
        ]);
    }
}
