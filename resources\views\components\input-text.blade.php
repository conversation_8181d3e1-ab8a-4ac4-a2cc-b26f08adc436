@props([
    'label' => '',
    'name',
    'value' => '',
    'placeholder' => '',
    'type' => 'text',
    'required' => false,
    'id' => $name,
])

<div class="mb-4">
    @if ($label)
        <label for="{{ $id }}" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
            {{ $label }}  {!! $required ? '<span class="text-red-500">*</span>' : '' !!}
        </label>
    @endif
    <input type="{{ $type }}" id="{{ $id }}" name="{{ $name }}" value="{{ old($name, $value) }}"
        placeholder="{{ $placeholder }}" {{ $required ? 'required' : '' }}
        {{ $attributes->merge([
            'class' =>
                'form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent',
        ]) }}
         />
</div>
