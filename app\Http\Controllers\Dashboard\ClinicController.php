<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;

class ClinicController extends Controller
{
    use AuthorizesRequests;

    public function index(Clinic $clinic)
    {
        $this->authorize('read clinics');
        $title = __("Clinics Management");
        $clinics = $clinic->get();
        return view('dashboard.clinics.index', compact('clinics', 'title'));
    }

    public function create()
    {
        $title = __("Create New Clinic");
        return view('dashboard.clinics.create', compact('title'));
    }

    public function store(Request $request)
    {
        $this->authorize('create clinics');

        // Validate the incoming request data
        $data = $request->validate([
            'name'          => 'required|string|max:255',
            'description'   => 'nullable|string',
            'morning_shift' => 'nullable|string|max:255',
            'evening_shift' => 'nullable|string|max:255',
            'closed_day'    => 'nullable|string|max:255',
        ]);

        Clinic::create($data);

        return redirect()->route('clinics.index')
            ->with('success', __("Clinic created successfully."));
    }

    public function edit(string $id)
    {
        $clinic = Clinic::findOrFail($id);
        $title = __("Edit Clinic");
        return view('dashboard.clinics.edit', compact("clinic", 'title'));
    }

    public function update(Request $request, string $id)
    {
        $this->authorize('update clinics');

        // Validate the incoming request data
        $data = $request->validate([
            'name'          => 'required|string|max:255',
            'description'   => 'nullable|string',
            'morning_shift' => 'nullable|string|max:255',
            'evening_shift' => 'nullable|string|max:255',
            'closed_day'    => 'nullable|string|max:255',
        ]);

        $clinic = Clinic::findOrFail($id);
        $clinic->update($data);

        return redirect()->route('clinics.index')
            ->with('success', __("Clinic updated successfully."));
    }

    public function destroy(string $id)
    {
        $this->authorize('delete clinics');
        $clinic = Clinic::findOrFail($id);
        $clinic->delete();
        return redirect()->route('clinics.index')
            ->with('success', __("Clinic deleted successfully."));
    }
}
