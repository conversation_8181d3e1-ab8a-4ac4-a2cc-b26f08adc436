<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\ChatMessage;
use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\Rating;
use App\Models\WaitingTicket; // Assuming you have this model
use DB;

class DashboardController extends Controller
{

    public function dashboardsCrmAnalytics()
    {
        // Get overall appointment counts.
        $totalAppointments = WaitingTicket::count();
        $totalCompleted = WaitingTicket::where('status', 'confirmed')->count();
        $totalPending   = WaitingTicket::where('status', 'waiting')->count();
        $totalAssigned  = WaitingTicket::where('status', 'assigned')->count();
        $totalClinics  = Clinic::count();
        $totalClientMessages  = ChatMessage::where('sender_type', 'client')->count();
        $totalBotMessages  = ChatMessage::where('sender_type', 'bot')->count();
        $last_booked_clinics = WaitingTicket::latest()->take(3)->get();
        $doctors = Doctor::get();
        // Get appointment data grouped by month (for the last year, for example).
        $appointmentsPerMonth = WaitingTicket::selectRaw("DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count")
            ->where('created_at', '>=', now()->subYear())
            ->groupBy('month')
            ->orderBy('month', 'asc')
            ->get();
        $ratings = Rating::get();

        $chartLabels = $appointmentsPerMonth->pluck('month')->toArray();
        $chartData   = $appointmentsPerMonth->pluck('count')->toArray();
        // Aggregating Ratings Data:
        $totalRatings = \App\Models\Rating::count();
        $averageRating = \App\Models\Rating::avg('rating');

        // Get count per rating value using a groupBy query.
        $ratingsGrouped = \App\Models\Rating::selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->pluck('count', 'rating'); // Returns a collection with keys as rating values.

        // Calculate percentage for each rating from 1 to 5.
        $ratingsPercentages = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = $ratingsGrouped->get($i, 0);
            $ratingsPercentages[$i] = $totalRatings > 0 ? round(($count / $totalRatings) * 100) : 0;
        }
        return view('dashboard.crm.index', compact(
            'totalAppointments',
            'totalCompleted',
            'totalPending',
            'totalBotMessages',
            'totalAssigned',
            'chartLabels',
            'chartData',
            'totalClinics',
            'totalClientMessages',
            'last_booked_clinics',
            'doctors',
            'totalRatings',
            'averageRating',
            'ratingsGrouped',
            'ratingsPercentages'
        ));
    }
}
