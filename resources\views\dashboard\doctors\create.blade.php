<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        @can('create doctors')
            <div class="flex items-center justify-between py-5 lg:py-6">
                <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">{{ $title }}</h2>
            </div>
        @endcan

        <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
            <form action="{{ route('doctors.store') }}" method="POST">
                @csrf
                <x-input-text :label="__('Name')" name="name" placeholder="Enter Doctor name" required />
                <x-select-input :label="__('Clinic')" name="clinic_id" :options="$clinics->pluck('name', 'id')->toArray()" value="{{ old('clinic_id') }}"
                    required />
                <x-input-text :label="__('Schedule')" name="schedule"
                    placeholder="Enter Doctor Schedule (e.g., السبت - الثلاثاء, 8 ص - 12 م)" />
                <x-save-create-button route="{{ route('doctors.index') }}" />
            </form>
        </div>
    </main>
</x-app-layout>
