<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class UserHandoffRequested extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * User phone who requested the handoff.
     *
     * @var string
     */
    public $clientPhone;

    /**
     * Admin user ID to broadcast on.
     *
     * @var int
     */
    public $userId;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $clientPhone, int $userId)
    {
        $this->clientPhone = $clientPhone;
        $this->userId      = $userId;
    }

    /**
     * Channels to send the notification on.
     */
    public function via($notifiable)
    {
        return ['broadcast', 'database'];
    }

    /**
     * Data stored in the notifications table.
     */
    public function toDatabase($notifiable)
    {
        return [
            'message'      => "User {$this->clientPhone} requested a human handoff.",
            'client_phone' => $this->clientPhone,
        ];
    }

    /**
     * Data broadcasted to the frontend.
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'message'      => "User {$this->clientPhone} requested a human handoff.",
            'client_phone' => $this->clientPhone,
        ]);
    }

    /**
     * Broadcast on the private channel for this admin user.
     */
    public function broadcastOn()
    {
        return new PrivateChannel("App.Models.User.{$this->userId}");
    }
}
