2025/03/11-04:50:37.403 db845 Reusing MANIFEST /home/<USER>/projects/booking-app/backend/server/whatsapp/.wwebjs_auth/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/03/11-04:50:37.403 db845 Recovering log #100
2025/03/11-04:50:37.410 db845 Reusing old log /home/<USER>/projects/booking-app/backend/server/whatsapp/.wwebjs_auth/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb/000100.log 
2025/03/11-04:50:47.312 db875 Level-0 table #106: started
2025/03/11-04:50:47.317 db875 Level-0 table #106: 122238 bytes OK
2025/03/11-04:50:47.320 db875 Delete type=0 #100
2025/03/11-04:50:47.320 db875 Manual compaction at level-0 from '\x00\x1b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x1c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/03/11-04:50:49.188 db872 Compacting 1@1 + 2@2 files
2025/03/11-04:50:49.257 db872 Generated table #107@1: 67149 keys, 2243706 bytes
2025/03/11-04:50:49.318 db872 Generated table #108@1: 49490 keys, 1923630 bytes
2025/03/11-04:50:49.318 db872 Compacted 1@1 + 2@2 files => 4167336 bytes
2025/03/11-04:50:49.321 db872 compacted to: files[ 0 0 2 0 0 0 0 ]
2025/03/11-04:50:49.321 db872 Delete type=2 #102
2025/03/11-04:50:49.321 db872 Delete type=2 #106
2025/03/11-04:50:49.322 db872 Delete type=2 #103
