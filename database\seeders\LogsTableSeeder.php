<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Log;

class LogsTableSeeder extends Seeder
{
    public function run()
    {
        // Create 10 random log entries
        Log::factory(10)->create();

        // Optionally create a specific log example
        Log::factory()->create([
            'action_type' => 'appointment.created',
            'old_values'  => null,
            'new_values'  => json_encode(['status' => 'pending', 'patient' => '<PERSON>']),
        ]);

        $this->command->info('Logs seeded.');
    }
}
