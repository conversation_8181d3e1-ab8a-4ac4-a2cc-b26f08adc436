// whatsapp.js (ES Module)

import pkg from 'whatsapp-web.js';
import qrcode from 'qrcode-terminal';
import axios from 'axios';
import express from 'express';
import bodyParser from 'body-parser';

const { Client, LocalAuth } = pkg;

// Create an Express app
const app = express();
app.use(bodyParser.json());

// Initialize the WhatsApp client using LocalAuth to persist sessions
const client = new Client({
  authStrategy: new LocalAuth(),
  puppeteer: {
    args: ['--no-sandbox'],
  },
});

// When a QR code is received, display it for scanning
client.on('qr', (qr) => {
  console.log('QR RECEIVED, scan it with your WhatsApp app:');
  qrcode.generate(qr, { small: true });
});

// Log when the client is ready
client.on('ready', () => {
  console.log('WhatsApp client is ready!');
});

// Listen for incoming WhatsApp messages
client.on('message', async (message) => {
  console.log(`Message from ${message.from}: ${message.body}`);

  // Skip group messages
  if (message.from.endsWith('@g.us')) {
    console.log('Message is from a group, ignoring.');
    return;
  }

  // Skip empty messages
  if (!message.body || message.body.trim() === '') {
    console.log('Empty message received, ignoring.');
    return;
  }

  // Format the client's phone number (remove '@c.us' and add '+' prefix)
  const clientPhone = '+' + message.from.split('@')[0];

  const requestData = {
    client_phone: clientPhone,
    message: message.body,
  };

  try {
    console.log('Sending message to Laravel API...');
    // Replace with your Laravel API endpoint URL
    const response = await axios.post('http://localhost:8000/api/whatsapp-message', requestData);
    console.log('Response from Laravel:', response.data);

    // If botReplies exist, send them back via WhatsApp
    if (response.data.botReplies && response.data.botReplies.length > 0) {
      for (const reply of response.data.botReplies) {
        if (reply.text) {
          await client.sendMessage(message.from, reply.text);
        }
      }
    } else {
      // Fallback reply if no bot reply is returned
      await client.sendMessage(message.from, 'Your message has been received.');
    }
  } catch (error) {
    console.error(
      'Error sending message to Laravel:',
      error.response ? error.response.data : error.message
    );
    await client.sendMessage(message.from, 'There was an error processing your request. Please try again later.');
  }
});

// Optional Express endpoint to send WhatsApp messages (for admin replies)
app.post('/send-message', async (req, res) => {
  let { phone, message } = req.body;
  phone = phone.replace(/^\+/, '');
  const chatId = `${phone}@c.us`;

  try {
    const sent = await client.sendMessage(chatId, message);
    console.log('Message sent:', sent);
    res.status(200).json({ success: true, sent });
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Start the Express server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`WhatsApp service running on port ${PORT}`));

// Initialize the WhatsApp client
client.initialize();
