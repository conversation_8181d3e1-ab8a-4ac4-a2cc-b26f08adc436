<?php

use App\Http\Controllers\ChatController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\WhatsAppController;
use Illuminate\Support\Facades\Route;

Route::get('/chat/{clientPhone}', [ChatController::class, 'getConversation'])
    ->name('api.chat.getConversation');
Route::post('/chat', [ChatController::class, 'store'])
    ->name('api.chat.store');
Route::middleware('auth:sanctum')->get('/notifications', [NotificationController::class, 'index']);
Route::post('/whatsapp-message', [WhatsAppController::class, 'store']);
