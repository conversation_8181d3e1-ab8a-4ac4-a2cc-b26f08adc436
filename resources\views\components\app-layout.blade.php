<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">

    <link rel="icon" type="image/png" href="{{ asset('favicon.png') }}" />

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ config('app.name') }} @isset($title)
            - {{ $title }}
        @endisset
    </title>
    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <!-- CSS & JS Assets -->
    @if (app()->getLocale() === 'ar')
        @vite(['resources/ar/css/app.css', 'resources/ar/js/app.js'])
    @else
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <style>
            .pr-main-sidebar-width {
                padding-right: var(--main-sidebar-width);
            }
        </style>
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
        rel="stylesheet" />
    <script>
        /**
         * THIS SCRIPT REQUIRED FOR PREVENT FLICKERING IN SOME BROWSERS
         */
        localStorage.getItem("_x_darkMode_on") === "true" &&
            document.documentElement.classList.add("dark");
    </script>
    <link href="https://unpkg.com/filepond/dist/filepond.css" rel="stylesheet">
    <script src="https://unpkg.com/filepond/dist/filepond.js"></script>
    @isset($head)
        {{ $head }}
    @endisset
    @stack('styles')

</head>

<body x-data x-bind="$store.global.documentBody"
    class="@isset($isSidebarOpen) {{ $isSidebarOpen === 'true' ? 'is-sidebar-open' : '' }} @endisset @isset($isHeaderBlur) {{ $isHeaderBlur === 'true' ? 'is-header-blur' : '' }} @endisset @isset($hasMinSidebar) {{ $hasMinSidebar === 'true' ? 'has-min-sidebar' : '' }} @endisset  @isset($headerSticky) {{ $headerSticky === 'false' ? 'is-header-not-sticky' : '' }} @endisset">
    <!-- App preloader-->
    <x-app-preloader></x-app-preloader>

    <!-- Page Wrapper -->
    <div id="root" class="min-h-100vh flex grow bg-slate-50 dark:bg-navy-900" x-cloak>
        <!-- Sidebar -->
        <div class="sidebar print:hidden">
            <!-- Main Sidebar -->
            <x-app-partials.main-sidebar></x-app-partials.main-sidebar>

            <!-- Sidebar Panel -->
            <x-app-partials.sidebar-panel></x-app-partials.sidebar-panel>
        </div>

        <!-- App Header -->
        <x-app-partials.header></x-app-partials.header>

        <!-- Mobile Searchbar -->
        {{-- <x-app-partials.mobile-searchbar></x-app-partials.mobile-searchbar> --}}

        <!-- Right Sidebar -->
        <x-app-partials.right-sidebar></x-app-partials.right-sidebar>

        {{ $slot }}

    </div>

    <!--
  This is a place for Alpine.js Teleport feature
  @see https://alpinejs.dev/directives/teleport
-->
    <div id="x-teleport-target"></div>

    <script>
        window.addEventListener("DOMContentLoaded", () => Alpine.start());
    </script>

    @isset($script)
        {{ $script }}
    @endisset
    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <script>
        window.addEventListener("DOMContentLoaded", () => {
            @if (session('success'))
                Toastify({
                    text: "{{ session('success') }}",
                    duration: 2000,
                    gravity: "top", // Position the toast at the top
                    position: "right", // Display on the right side
                    backgroundColor: "linear-gradient(to right, #00b09b, #96c93d)", // Green gradient
                }).showToast();
            @elseif (session('error'))
                Toastify({
                    text: "{{ session('error') }}",
                    duration: 2000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "linear-gradient(to right, #ff5f6d, #ffc371)", // Red/Orange gradient
                }).showToast();
            @endif
        });
    </script>
    @if ($errors->any())
        @foreach ($errors->all() as $error)
            <script>
                Toastify({
                    text: "{{ $error }}",
                    duration: 2000,
                    gravity: "top", // Position the toast at the top
                    position: "right", // Display on the right side
                    backgroundColor: "linear-gradient(to right, #ff5f6d, #ffc371)", // Red/Orange gradient
                }).showToast();
            </script>
        @endforeach
    @endif
    <script>
        FilePond.setOptions({
            server: {
                url: '/filepond/api',
                process: {
                    url: "/process",
                    headers: (file) => {
                        // Send the original file name which will be used for chunked uploads
                        return {
                            "Upload-Name": file.name,
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        }
                    },
                },
                revert: '/process',
                patch: "?patch=",
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            }
        });
    </script>
    @stack('scripts')
</body>

</html>
