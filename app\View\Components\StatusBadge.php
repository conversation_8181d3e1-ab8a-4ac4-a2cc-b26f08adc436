<?php

namespace App\View\Components;

use Illuminate\View\Component;

class StatusBadge extends Component
{
    public $status;
    public $classes;

    public function __construct($status)
    {
        $this->status = $status;
        $statusKey = strtolower($status);
        $this->classes = config("appointment_statuses.$statusKey", config('appointment_statuses.default'));
    }

    public function render()
    {
        return view('components.status-badge');
    }
}
