<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <!-- Header -->
        <div class="flex items-center justify-between py-5 lg:py-6">
            <h2 class="text-2xl font-medium text-slate-800 dark:text-navy-50">
                {{ $title }}
            </h2>
        </div>
        <!-- Form Card -->
        <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
            <form action="{{ route('clinic-offers.update', $clinic_offer) }}" method="POST" enctype="multipart/form-data">
                @csrf @method('put')

                <!-- Name Field -->
                <x-input-text :label="__('Title')" name="title" :value="$clinic_offer->title" placeholder="Enter title" required />
                <x-input-text :label="__('Price')" name="price" :value="$clinic_offer->price" placeholder="Enter Price"
                    type="number" required />
                <x-textarea :label="__('Description')" name="description" :value="$clinic_offer->description" placeholder="Enter description" />
                <div class="mb-4">
                    <label for="image" class="block text-sm font-medium text-slate-700 dark:text-navy-100 mb-1">
                        {{ __('Image') }}
                    </label>
                    <input type="file" id="image" name="image"
                        class="form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent">
                </div>
                <!-- Form Actions -->
                <x-save-edit-button route="{{ route('clinic-offers.index') }}" />
            </form>
        </div>
    </main>
</x-app-layout>
