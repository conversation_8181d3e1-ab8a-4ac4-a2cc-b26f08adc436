<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Appointment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id', 'patient_name', 'patient_phone',
        'status', 'appointment_date', 'notes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
