<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    /**
     * NotificationController constructor.
     *
     * Apply the auth middleware so only authenticated users can access these routes.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Return unread notifications with a transformed message field.
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Since the auth middleware is applied, $request->user() is always available.
        $user = $request->user();

        // Transform the notifications so that each notification has a top-level 'message' field.
        $notifications = $user->unreadNotifications->map(function ($notification) {
            return [
                'id'         => $notification->id,
                'message'    => $notification->data['message'] ?? 'No message available',
                'created_at' => $notification->created_at->toDateTimeString(),
            ];
        });

        return response()->json([
            'notifications' => $notifications,
            'unread_count'  => $user->unreadNotifications->count(),
        ]);
    }

    /**
     * Mark all unread notifications as read for the authenticated user.
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function readAll(Request $request): JsonResponse
    {
        $user = $request->user();

        try {
            // Mark all unread notifications as read.
            $user->unreadNotifications->markAsRead();

            return response()->json(['message' => 'All notifications marked as read'], 200);
        } catch (\Exception $e) {
            // Log the exception details for debugging.
            Log::error('Error marking notifications as read: ' . $e->getMessage());

            return response()->json(['message' => 'Unable to mark notifications as read'], 500);
        }
    }
}
