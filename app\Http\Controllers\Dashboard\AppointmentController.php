<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\WaitingTicket;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AppointmentsExport; // Create this export class

class AppointmentController extends Controller
{
    /**
     * Constructor to apply middleware for authorization.
     */
    public function __construct()
    {
        // This will ensure that only Receptionists and Super Admins can access these methods.
        $this->middleware(function ($request, $next) {
            if (! $request->user()->hasAnyRole(['Receptionist', 'Super Admin'])) {
                abort(403, 'Unauthorized action.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the appointments.
     */
    public function index(Request $request, $status = null)
    {
        // Determine the status from route parameter or request query string; default is 'waiting'
        $status = $status ?? $request->get('status', 'waiting');
        $title = $status === 'waiting'
            ? __("Waiting Appointments")
            : __("Assigned Appointments");

        $appointments = WaitingTicket::with('doctor')
            ->where('status', $status)
            ->orderBy('id', 'DESC')
            ->paginate(10);

        $totalWaiting  = WaitingTicket::where('status', 'waiting')->count();
        $totalAssigned = WaitingTicket::where('status', 'assigned')->count();
        $doctors       = Doctor::all();

        return view("dashboard.appointments.index", compact(
            'appointments',
            'totalWaiting',
            'totalAssigned',
            'status',
            'doctors',
            'title'
        ));
    }

    /**
     * Export appointments as an Excel file.
     */
    public function export(Request $request)
    {
        $status = $request->get('status', 'waiting');
        $appointments = WaitingTicket::with('doctor_data')
            ->where('status', $status)
            ->orderBy('id', 'DESC')
            ->get();

        // Download the Excel file (ensure you have created AppointmentsExport)
        return Excel::download(
            new AppointmentsExport($appointments),
            'appointments_' . $status . '.xlsx'
        );
    }

    /**
     * Show the create appointment form.
     */
    public function create(Request $request)
    {
        $doctors = Doctor::all();
        $clinics = Clinic::all();
        $status = $request->status;

        return view('dashboard.appointments.create', compact('doctors', 'status', 'clinics'));
    }

    /**
     * Store a newly created appointment in storage.
     */
    public function store(Request $request)
    {
        // Validate the incoming request data.
        $data = $request->validate([
            'full_name'        => 'required|string|max:255',
            'client_phone'     => 'required|string|max:20',
            'identity_number'  => 'required|string|max:255',
            'birth_date'       => 'required|date',
            'clinic'           => 'required|string|max:255',
            'doctor_id'        => 'required|exists:doctors,id',
            'shift'            => 'required|string|max:255',
            'payment_method'   => 'required|string|max:255',
            'appointment_date' => 'required|date',
            'status'           => 'required|in:waiting,assigned',
            'waiting_number'   => 'nullable|string',
        ]);

        // Associate the appointment with the authenticated user.
        $data['user_id'] = auth()->id();

        // Create the appointment.
        $appointment = WaitingTicket::create($data);

        // If a waiting number is provided, send a WhatsApp message.
        if (! empty($data['waiting_number'])) {
            // The doctor will be retrieved inside the helper method if needed.
            $this->sendWhatsAppMessage($appointment);
        }

        return redirect()
            ->route('appointments.' . $data['status'])
            ->with('success', __("Appointment created successfully."));
    }


    /**
     * Show the form for editing the specified appointment.
     */
    public function edit(WaitingTicket $appointment, Request $request)
    {
        $clinics = Clinic::all();
        $doctors = Doctor::all();
        $status  = $request->status;
        return view('dashboard.appointments.edit', compact('appointment', 'status', 'clinics', 'doctors'));
    }

    /**
     * Update the specified appointment in storage.
     */
    public function update(Request $request, WaitingTicket $appointment)
    {
        $data = $request->validate([
            'full_name'        => 'required|string|max:255',
            'client_phone'     => 'required|string|max:255',
            'identity_number'  => 'required|string|max:255',
            'birth_date'       => 'required|date',
            'clinic'           => 'required|string|max:255',
            'doctor_id'        => 'required|exists:doctors,id',
            'shift'            => 'required|string|max:255',
            'payment_method'   => 'required|string|max:255',
            'appointment_date' => 'required|date',
            'status'           => 'required|in:waiting,assigned',
            'waiting_number'   => 'nullable|string',
        ]);

        try {
            $appointment->update($data);
            return redirect()->back()
                ->with('success', __("Appointment updated successfully."));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', __("Failed to update appointment."));
        }
    }


    /**
     * Remove the specified appointment from storage.
     */
    public function destroy(WaitingTicket $appointment, Request $request)
    {
        try {
            $appointment->delete();
            return redirect()->route('appointments.' . $request->status)
                ->with('success', __("Appointment deleted successfully."));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', __("Failed to delete appointment."));
        }
    }

    /**
     * Assign a waiting number to an appointment and send it via WhatsApp.
     */
    public function assignWaitingNumber(WaitingTicket $appointment, Request $request)
    {
        // Validate the waiting number and optional doctor id.
        $validated = $request->validate([
            'waiting_number' => 'required|string',
            'doctor_id'      => 'nullable|exists:doctors,id',
        ]);

        $clinic = Clinic::where('name', $appointment->clinic)->first();

        $appointment->update([
            'waiting_number' => $validated['waiting_number'],
            'doctor_id'      => $validated['doctor_id'] ?? null,
            'clinic_id'      => $clinic ? $clinic->id : null,
            'status'         => 'assigned'
        ]);

        $this->sendWhatsAppMessage($appointment);

        return redirect()->back()
            ->with('success', __("Appointment updated and waiting number sent via WhatsApp."));
    }

    /**
     * Private helper method to send a WhatsApp message via the Node.js service.
     * This method uses the data directly from the WaitingTicket model.
     *
     * @param \App\Models\WaitingTicket $appointment The waiting ticket appointment.
     */
    private function sendWhatsAppMessage(WaitingTicket $appointment)
    {
        $doctorName = "غير محدد";
        if (!empty($appointment->doctor_id)) {
            $doctor = Doctor::find($appointment->doctor_id);
            if ($doctor) {
                $doctorName = $doctor->name;
            }
        }

        $message = "✅ **تم تسجيل طلب الموعد بنجاح:**\n" .
            "📝 *الاسم:* " . $appointment->full_name . "\n" .
            "📅 *تاريخ الميلاد:* " . $appointment->birth_date . "\n" .
            "🆔 *رقم الهوية:* " . $appointment->identity_number . "\n" .
            "🏥 *التخصص:* " . $appointment->clinic . "\n" .
            "👨‍⚕️ *الدكتور:* " . $doctorName . "\n" .
            "📅 *تاريخ الموعد:* " . $appointment->appointment_date . "\n" .
            "⏰ *الفترة:* " . $appointment->shift . "\n" .
            "💳 *طريقة الدفع:* " . $appointment->payment_method . "\n\n" .
            "📌 *رقم الحجز:* " . $appointment->id . "\n" .
            "🕒 *تاريخ التسجيل:* " . now()->format('Y-m-d') . "\n\n" .
            "شكراً لحجزك موعداً معنا!.\n" .
            "📢 *إذا كان لديك أي استفسار، يُرجى الاتصال بالعيادة.*";

        try {
            $client = new Client();
            $client->post('http://localhost:3000/send-message', [
                'json' => [
                    'phone'   => $appointment->client_phone,
                    'message' => $message,
                ]
            ]);
        } catch (\Exception $e) {
            logger()->error("Error sending WhatsApp message: " . $e->getMessage());
        }
    }
}
