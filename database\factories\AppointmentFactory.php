<?php

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class AppointmentFactory extends Factory
{
    protected $model = Appointment::class;

    public function definition()
    {
        // Randomly pick a future date 1–30 days from now
        $randomDate = Carbon::now()->addDays($this->faker->numberBetween(1, 30));

        return [
            'user_id'         => User::factory(), // creates a user if not given
            'patient_name'    => $this->faker->name(),
            'patient_phone'   => $this->faker->phoneNumber(),
            'status'          => $this->faker->randomElement(['pending', 'confirmed', 'rejected']),
            'appointment_date'=> $randomDate,
            'notes'           => $this->faker->sentence(),
        ];
    }
}
