<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConversationLogsTable extends Migration
{
    public function up()
    {
        Schema::create('conversation_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('sender_id');
            $table->text('message');
            $table->string('speaker', 50);
            $table->timestamp('timestamp')->useCurrent();
        });
    }

    public function down()
    {
        Schema::dropIfExists('conversation_logs');
    }
}
