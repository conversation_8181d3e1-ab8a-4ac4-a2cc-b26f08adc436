<x-app-layout title="Edit Role" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="flex items-center justify-between py-5 lg:py-6">
            <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">{{ __("Edit Role") }}</h2>
        </div>
        <div class="card p-4 sm:p-5 shadow-lg rounded-lg">
            <form action="{{ route('roles.update', $role->id) }}" method="POST">
                @csrf
                @method('PUT')
                <x-input-text :label="__('Name')" name="name" value="{{ old('name', $role->name) }}" required />

                <div class="mb-4">
                    <label class="block text-lg font-medium text-slate-700 dark:text-navy-100 mb-2">{{ __("Permissions") }}</label>
                    <div class="grid grid-cols-2 gap-2">
                        @foreach ($permissions as $permission)
                            <label class="inline-flex items-center space-x-2">
                                <input value="{{ $permission->name }}"
                                    {{ in_array($permission->name, $role->getPermissionNames()->toArray()) ? 'checked' : '' }}
                                    class="form-checkbox is-basic size-5 rounded-sm border-slate-400/70 checked:bg-primary checked:border-primary hover:border-primary focus:border-primary dark:border-navy-400 dark:checked:bg-accent dark:checked:border-accent dark:hover:border-accent dark:focus:border-accent"
                                    type="checkbox" name="permissions[]" />
                                <p>{{ __('permissions.'.$permission->name) }}</p>
                            </label>
                        @endforeach
                    </div>
                </div>
                <x-save-create-button route="{{ route('roles.index') }}" />
            </form>
        </div>
    </main>
</x-app-layout>
