<x-base-layout :title="$title" is-sidebar-open="true" has-min-sidebar="true">
    <!-- Sidebar (unchanged) -->
    <div class="sidebar print:hidden">
        <!-- Main Sidebar -->
        <x-app-partials.main-sidebar></x-app-partials.main-sidebar>
        <!-- Sidebar Panel -->
        <div class="sidebar-panel">
            <div class="flex h-full grow flex-col bg-white {{ app()->getLocale() == 'ar' ? 'pr-[var(--main-sidebar-width)]' : 'pl-[var(--main-sidebar-width)]' }} dark:bg-navy-750">
                <!-- Sidebar Panel Header -->
                <div class="flex h-18 w-full items-center justify-between {{ app()->getLocale() == 'ar' ? 'pr' : 'pl' }}-4 pr-1">
                    <div class="flex items-center">
                        <div class="avatar mr-3 hidden size-9 lg:flex">
                            <div
                                class="is-initial rounded-full bg-primary/10 text-primary dark:bg-accent-light/10 dark:text-accent-light">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                            </div>
                        </div>
                        <p class="text-lg font-medium tracking-wider text-slate-800 line-clamp-1 dark:text-navy-100">
                            Chat</p>
                    </div>
                    <button @click="$store.global.isSidebarExpanded = false"
                        class="btn size-7 rounded-full p-0 text-primary hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-accent-light/80 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25 xl:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                </div>
                <!-- (Sidebar search input and conversation list remain unchanged) -->
                <div class="is-scrollbar-hidden mt-3 flex grow flex-col overflow-y-auto">
                    <div class="flex items-center justify-between px-4">
                        <span class="text-xs-plus font-medium uppercase">{{ __("Chats") }}</span>
                        <div class="-mr-1.5 flex">
                            <button
                                class="btn size-6 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-3.5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    @forelse ($clientConversations as $conversation)
                        <div class="flex cursor-pointer items-center space-x-2.5 px-4 py-2.5 font-inter hover:bg-slate-150 dark:hover:bg-navy-600"
                            @click="$dispatch('conversation-selected', { clientPhone: '{{ $conversation->client_phone }}' })">
                            <div class="avatar size-10">
                                <img class="rounded-full" src="{{ asset('images/200x200.png') }}" alt="avatar" />
                            </div>
                            <div class="flex flex-1 flex-col">
                                <div class="flex items-baseline justify-between space-x-1.5">
                                    <p class="text-xs-plus font-medium text-slate-700 line-clamp-1 dark:text-navy-100">
                                        {{ $conversation->client_phone }}
                                    </p>
                                    <span class="text-tiny-plus text-slate-400 dark:text-navy-300">
                                        {{ \Carbon\Carbon::parse($conversation->last_message_time)->format('H:i') }}
                                    </span>
                                </div>
                                <div class="mt-1 flex items-center justify-between space-x-1">
                                    <p class="text-xs-plus text-slate-400 line-clamp-1 dark:text-navy-300">
                                        {{ $conversation->last_message }}
                                    </p>
                                    <div
                                        class="flex h-4.5 min-w-[1.125rem] items-center justify-center rounded-full bg-slate-200 px-1.5 text-tiny-plus font-medium leading-none text-slate-800 dark:bg-navy-450 dark:text-white">
                                        {{ $conversation->messages_count }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="px-4 py-2 text-center text-slate-500">No conversations found.</p>
                    @endforelse
                </div>

            </div>
        </div>
    </div>

    <!-- Main Chat Panel with file upload support -->
    <main x-data="chatApp()" @conversation-selected.window="loadConversation($event.detail.clientPhone)"
        class="main-content h-100vh chat-app mt-0 flex w-full flex-col text-right"
        :class="isShowChatInfo ? 'lg:mr-80' : ''">
        <!-- Chat Header (unchanged) -->
        <div
            class="chat-header h-[61px] border-b border-slate-150 dark:border-navy-700 relative z-10 flex w-full shrink-0 items-center justify-between bg-white px-[calc(var(--margin-x)-.5rem)] shadow-xs transition-[padding,width] duration-[.25s] dark:bg-navy-800">
            <div class="flex items-center space-x-5">
                <div class="ml-1 size-7">
                    <button
                        class="menu-toggle cursor-pointer ml-0.5 flex size-7 flex-col justify-center space-y-1.5 text-primary outline-hidden focus:outline-hidden dark:text-accent-light/80"
                        :class="$store.global.isSidebarExpanded && 'active'"
                        @click="$store.global.isSidebarExpanded = !$store.global.isSidebarExpanded">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
                <div @click="isShowChatInfo = true" class="flex cursor-pointer items-center space-x-4 font-inter">
                    <div class="avatar">
                        <img class="rounded-full" :src="activeChat.avatar_url" alt="avatar" />
                    </div>
                    <div>
                        <p class="font-medium text-slate-700 line-clamp-1 dark:text-navy-100" x-text="activeChat.name">
                        </p>
                        <p class="mt-0.5 text-xs">Last seen recently</p>
                    </div>
                </div>
            </div>
            <div class="-mr-1 flex items-center">
                <button @click="isShowChatInfo = !isShowChatInfo"
                    :class="isShowChatInfo ? 'text-primary dark:text-accent-light' : 'text-slate-500 dark:text-navy-200'"
                    class="btn hidden size-9 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25 sm:flex">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5" fill="none" stroke="currentColor"
                        stroke-width="1.5" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.25 21.167h5.5c4.584 0 6.417-1.834 6.417-6.417v-5.5c0-4.583-1.834-6.417-6.417-6.417h-5.5c-4.583 0-6.417 1.834-6.417 6.417v5.5c0 4.583 1.834 6.417 6.417 6.417ZM13.834 2.833v18.334" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="grow overflow-y-auto px-[calc(var(--margin-x)-.5rem)] py-5 transition-all duration-[.25s]">
            <template x-if="activeConversation">
                <div>
                    <template x-for="(msgs, date) in groupedMessages" :key="date">
                        <div>
                            <!-- Date Header -->
                            <div class="mx-4 flex items-center space-x-3">
                                <div class="h-px flex-1 bg-slate-200 dark:bg-navy-500"></div>
                                <p x-text="date"></p>
                                <div class="h-px flex-1 bg-slate-200 dark:bg-navy-500"></div>
                            </div>
                            <!-- Messages for this date -->
                            <template x-for="(msg, index) in msgs" :key="msg.id + '-' + index">
                                <div class="mb-2">
                                    <div
                                        :class="msg.sender_type === 'admin' || msg.sender_type === 'bot' ?
                                            'flex items-start justify-end space-x-2.5 sm:space-x-5' :
                                            'flex items-start space-x-2.5 sm:space-x-5'">
                                        <template x-if="msg.sender_type === 'client'">
                                            <div class="avatar">
                                                <img class="rounded-full" :src="activeChat.avatar_url" alt="avatar" />
                                            </div>
                                        </template>
                                        <div>
                                            <div
                                                :class="msg.sender_type === 'admin' || msg.sender_type === 'bot' ?
                                                    'rounded-2xl rounded-tr-none bg-info/10 p-3 text-slate-700 shadow-xs dark:bg-accent dark:text-white' :
                                                    'rounded-2xl rounded-tl-none bg-white p-3 text-slate-700 shadow-xs dark:bg-navy-700 dark:text-navy-100'">
                                                <p x-text="msg.message"></p>
                                                <template x-if="msg.file_path">
                                                    <div class="mt-2">
                                                        <template x-if="msg.file_path.match(/\.(jpeg|jpg|gif|png)$/i)">
                                                            <img :src="`/storage/${msg.file_path}`" alt="attachment"
                                                                class="max-w-xs rounded-md">
                                                        </template>
                                                        <template
                                                            x-if="!msg.file_path.match(/\.(jpeg|jpg|gif|png)$/i)">
                                                            <a :href="`/storage/${msg.file_path}`" target="_blank"
                                                                class="text-blue-500 underline block mt-2">
                                                                Download Attachment
                                                            </a>
                                                        </template>
                                                    </div>
                                                </template>
                                            </div>
                                            <span class="text-xs text-slate-400 block mt-1"
                                                x-text="(new Date(msg.created_at)).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})"></span>
                                        </div>
                                        <template x-if="msg.sender_type === 'admin'">
                                            <div class="avatar">
                                                <img class="rounded-full" src="{{ asset('images/200x200.png') }}"
                                                    alt="avatar" />
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </template>
            <template x-if="!activeConversation">
                <div class="text-center text-slate-500">
                    <p>Select a conversation from the sidebar</p>
                </div>
            </template>
        </div>



        <!-- Chat Footer (Message Input with File Upload) -->
        <div
            class="chat-footer relative flex h-12 w-full shrink-0 items-center justify-between border-t border-slate-150 bg-white px-[calc(var(--margin-x)-.25rem)] transition-[padding,width] duration-[.25s] dark:border-navy-600 dark:bg-navy-800">
            <div class="-ml-1.5 flex flex-1 items-center space-x-2">
                <!-- File Upload Button -->
                <button @click="$refs.fileInput.click()"
                    class="btn size-9 shrink-0 rounded-full p-0 text-slate-500 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-200 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                    </svg>
                </button>
                <!-- Hidden file input -->
                <input type="file" x-ref="fileInput" @change="handleFileUpload($event)" class="hidden">
                <!-- Text Message Input -->
                <input type="text" x-model="newMessage"
                    class="form-input h-9 w-full bg-transparent placeholder:text-slate-400/70"
                    placeholder="Write the message" />
            </div>
            <div class="-mr-1.5 flex">
                <button @click="sendMessage()"
                    class="btn size-9 shrink-0 rounded-full p-0 text-primary hover:bg-primary/20 focus:bg-primary/20 active:bg-primary/25 dark:text-accent-light dark:hover:bg-accent-light/20 dark:focus:bg-accent-light/20 dark:active:bg-accent-light/25">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5.5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="m9.813 5.146 9.027 3.99c4.05 1.79 4.05 4.718 0 6.508l-9.027 3.99c-6.074 2.686-8.553.485-5.515-4.876l.917-1.613c.232-.41.232-1.09 0-1.5l-.917-1.623C1.26 4.66 3.749 2.46 9.813 5.146ZM6.094 12.389h7.341" />
                    </svg>
                </button>
            </div>
        </div>
        <template x-teleport="#x-teleport-target">
            <div x-data="{
                get showDrawer() { return $data.isShowChatInfo; },
                set showDrawer(val) { $data.isShowChatInfo = val; },
            }" x-show="showDrawer" @keydown.window.escape="showDrawer = false">
                <div class="fixed inset-0 z-100 bg-slate-900/60 transition-opacity duration-200"
                    @click="showDrawer = false" x-show="showDrawer && $store.breakpoints.mdAndDown"
                    x-transition:enter="ease-out" x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100" x-transition:leave="ease-in"
                    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>
                <div class="fixed right-0 top-0 z-101 h-full w-full sm:w-80">
                    <div class="flex h-full w-full flex-col border-l border-slate-150 bg-white transition-transform duration-200 dark:border-navy-600 dark:bg-navy-750"
                        x-show="showDrawer" x-transition:enter="ease-out transform-gpu"
                        x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                        x-transition:leave="ease-in transform-gpu" x-transition:leave-start="translate-x-0"
                        x-transition:leave-end="translate-x-full">
                        <div class="flex h-[60px] items-center justify-between p-4">
                            <h3 class="text-base font-medium text-slate-700 dark:text-navy-100">
                                Chat Info
                            </h3>
                            <div class="-mr-1.5 flex space-x-1">
                                <button @click="$store.global.isRightSidebarExpanded = true"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="size-5.5 text-slate-500 dark:text-navy-100" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                </button>

                                <button @click="$store.global.isDarkModeEnabled = !$store.global.isDarkModeEnabled"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg x-show="$store.global.isDarkModeEnabled"
                                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                                        class="size-6 text-amber-400" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M11.75 3.412a.818.818 0 01-.07.917 6.332 6.332 0 00-1.4 3.971c0 3.564 2.98 6.494 6.706 6.494a6.86 6.86 0 002.856-.617.818.818 0 011.1 1.047C19.593 18.614 16.218 21 12.283 21 7.18 21 3 16.973 3 11.956c0-4.563 3.46-8.31 7.925-8.948a.818.818 0 01.826.404z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" x-show="!$store.global.isDarkModeEnabled"
                                        x-transition:enter="transition-transform duration-200 ease-out absolute origin-top"
                                        x-transition:enter-start="scale-75" x-transition:enter-end="scale-100 static"
                                        class="size-6 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <button @click="showDrawer=false"
                                    class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="mt-5 flex flex-col items-center">
                            <div class="avatar size-20">
                                <img class="rounded-full" :src="activeChat.avatar_url" alt="avatar" />
                            </div>
                            <h3 x-text="activeChat.name"
                                class="mt-2 text-lg font-medium text-slate-700 dark:text-navy-100"></h3>
                        </div>
                        <div x-data="{ activeTab: 'tabImages' }" class="tabs mt-6 flex flex-col">
                            <div class="is-scrollbar-hidden overflow-x-auto px-4">
                                <div class="tabs-list flex">
                                    <button @click="activeTab = 'tabImages'"
                                        :class="activeTab === 'tabImages' ?
                                            'border-primary dark:border-accent text-primary dark:text-accent-light' :
                                            'border-transparent hover:text-slate-800 focus:text-slate-800 dark:hover:text-navy-100 dark:focus:text-navy-100'"
                                        class="btn shrink-0 rounded-none border-b-2 px-3 py-2 font-medium">
                                        Images
                                    </button>
                                    <button @click="activeTab = 'tabFiles'"
                                        :class="activeTab === 'tabFiles' ?
                                            'border-primary dark:border-accent text-primary dark:text-accent-light' :
                                            'border-transparent hover:text-slate-800 focus:text-slate-800 dark:hover:text-navy-100 dark:focus:text-navy-100'"
                                        class="btn shrink-0 rounded-none border-b-2 px-3 py-2 font-medium">
                                        Files
                                    </button>
                                </div>
                            </div>
                            <div class="tab-content px-4 pt-4">
                                <div x-show="activeTab === 'tabImages'"
                                    x-transition:enter="transition-all duration-500 easy-in-out"
                                    x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                    x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]">
                                    <div class="grid grid-cols-4 gap-2">
                                        <img class="aspect-square rounded-lg object-cover object-center"
                                            src="{{ asset('images/800x600.png') }}" alt="image" />
                                    </div>
                                </div>
                                <div x-show="activeTab === 'tabFiles'"
                                    x-transition:enter="transition-all duration-500 easy-in-out"
                                    x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                    x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]">
                                    <div class="flex flex-col space-y-3.5">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="mask is-squircle flex size-11 items-center justify-center bg-secondary text-white">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="size-6" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="font-medium text-slate-700 dark:text-navy-100">
                                                    Slow Music
                                                </p>
                                                <div class="flex text-xs text-slate-400 dark:text-navy-300">
                                                    <span>03:12</span>
                                                    <div class="mx-2 my-1 w-px bg-slate-200 dark:bg-navy-500"></div>

                                                    <span>8.32 MB</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </main>

    <script>
        function chatApp() {
            return {
                // UI state
                isShowChatInfo: false,
                activeConversation: null,
                activeChat: {
                    chatId: null,
                    name: '',
                    avatar_url: '/images/200x200.png'
                },
                // Chat data
                messages: [],
                newMessage: '',
                attachedFile: null,

                // Load messages for a given conversation (client phone number)
                loadConversation(clientPhone) {
                    const sanitizedPhone = clientPhone.replace('+', '');
                    this.activeConversation = clientPhone;
                    fetch(`/api/chat/${clientPhone}`, {
                            credentials: 'include',
                            headers: {
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.text().then(text => {
                                    throw new Error(text || "Network response was not ok");
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            this.messages = data.messages;
                            this.activeChat = {
                                chatId: clientPhone,
                                name: clientPhone,
                                avatar_url: '/images/200x200.png'
                            };

                            // Subscribe to a presence channel for this conversation
                            window.Echo.join(`chat.${sanitizedPhone}`)
                                .here((users) => {
                                    console.log('Currently online:', users);
                                    // You can update a UI element to show online users
                                })
                                .joining((user) => {
                                    console.log('User joined:', user);
                                    // Update UI for new user presence
                                })
                                .leaving((user) => {
                                    console.log('User left:', user);
                                    // Update UI for user leaving
                                })
                                .listen('.ChatMessageSent', (e) => {
                                    console.log('New message received:', e.chatMessage);
                                    if (!this.messages.some(m => m.id === e.chatMessage.id)) {
                                        this.messages.push(e.chatMessage);
                                    }
                                });
                        })
                        .catch(error => console.error('Error loading conversation:', error));
                },


                // Handle file selection from the hidden file input
                handleFileUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.attachedFile = file;
                    }
                },

                // Send a new message (with an optional file attachment)
                sendMessage() {
                    if (this.newMessage.trim() === '' && !this.attachedFile) return;
                    let formData = new FormData();
                    formData.append('client_phone', this.activeConversation);
                    formData.append('message', this.newMessage);
                    if (this.attachedFile) {
                        formData.append('file', this.attachedFile);
                    }
                    fetch(`/api/chat`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: formData
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.text().then(text => {
                                    throw new Error(text || "Network response was not ok");
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Message sent:', data.message);
                            // Append the newly sent message to the messages array
                            this.messages.push(data.message);
                            // Reset the inputs
                            this.newMessage = '';
                            this.attachedFile = null;
                            this.$refs.fileInput.value = '';
                        })
                        .catch(error => console.error('Error sending message:', error));
                },

                // Computed property to group messages by date (used in the template)
                get groupedMessages() {
                    let groups = {};
                    this.messages.forEach(msg => {
                        let date = new Date(msg.created_at);
                        let dateStr = date.toLocaleDateString(); // e.g. "2/21/2025"
                        if (!groups[dateStr]) {
                            groups[dateStr] = [];
                        }
                        groups[dateStr].push(msg);
                    });
                    return groups;
                }
            }
        }
    </script>
</x-base-layout>
