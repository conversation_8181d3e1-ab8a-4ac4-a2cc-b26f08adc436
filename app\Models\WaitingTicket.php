<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class WaitingTicket extends Model
{
    use HasFactory;

    protected $table = 'waiting_tickets';

    protected $fillable = [
        'full_name',
        'client_phone',
        'identity_number',
        'birth_date',
        'clinic', // if you're storing the clinic name instead of its ID
        'doctor_id',
        'shift',
        'payment_method',
        'appointment_date',
        'status',
        'waiting_number',
        'user_id'
    ];

    protected $casts = [
        'birth_date'       => 'date',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
    ];

    /**
     * Get the doctor associated with the waiting ticket.
     *
     * @return BelongsTo
     */
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class, 'doctor_id');
    }

    /**
     * Get the doctor associated with the waiting ticket.
     *
     * @return BelongsTo
     */
    public function doctor_data(): BelongsTo
    {
        return $this->belongsTo(Doctor::class, 'doctor_id');
    }

    /**
     * Get the clinic associated with the waiting ticket.
     *
     * @return BelongsTo
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the user who created the waiting ticket.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the rating for the waiting ticket.
     *
     * @return HasOne
     */
    public function rating(): HasOne
    {
        return $this->hasOne(Rating::class);
    }
}
