<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * This seeder creates CRUD permissions for resources,
     * assigns them to roles, and removes any permissions
     * that no longer exist in the current configuration.
     *
     * @return void
     */
    public function run()
    {
        // Define CRUD resources and their respective actions.
        $crudResources = [
            'dashboards' => ['read'],
            'roles' => ['create', 'read', 'update', 'delete'],
            'appointments' => ['create', 'read', 'update', 'delete'],
            'users'        => ['create', 'read', 'update', 'delete'],
            'doctors'        => ['create', 'read', 'update', 'delete'],
            'clinics'        => ['create', 'read', 'update', 'delete'],
            'offers'        => ['create', 'read', 'update', 'delete'],
            'logs'         => ['read', 'delete'],
        ];

        $currentPermissions = [];

        // Build the current list of permissions.
        foreach ($crudResources as $resource => $actions) {
            foreach ($actions as $action) {
                $currentPermissions[] = "{$action} {$resource}";
            }
        }

        // Remove any permissions in the database that are not in the current list.
        Permission::whereNotIn('name', $currentPermissions)->delete();

        // Create or update the permissions from the current list.
        foreach ($currentPermissions as $permissionName) {
            Permission::updateOrCreate(
                ['name' => $permissionName, 'guard_name' => 'web'],
                ['name' => $permissionName, 'guard_name' => 'web']
            );
        }

        // Define role permissions assignments.
        $rolesPermissions = [
            'Super Admin'  => $currentPermissions, // All permissions.
            'Receptionist' => [
                'create appointments',
                'read appointments',
                'update appointments',
                'delete appointments',
                'read logs'
            ],
            // 'Doctor'       => [
            //     'read appointments',
            //     'update appointments'
            // ],
        ];

        // Create each role and assign the defined permissions.
        foreach ($rolesPermissions as $roleName => $perms) {
            $role = Role::updateOrCreate(
                ['name' => $roleName, 'guard_name' => 'web'],
                ['name' => $roleName, 'guard_name' => 'web']
            );
            $role->syncPermissions($perms);
        }

        $this->command->info('Roles & CRUD Permissions seeded successfully.');
    }
}
