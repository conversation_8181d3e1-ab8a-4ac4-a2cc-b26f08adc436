<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChatMessage;
use App\Models\User;
use App\Notifications\ChatNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http; // we'll use Http facade to call our WhatsApp service
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class ChatController extends Controller
{
    public function index()
    {
        $title = __("Chats");
        // Retrieve distinct client conversations with aggregated data
        $clientConversations = ChatMessage::selectRaw('client_phone, MAX(created_at) as last_message_time, MAX(message) as last_message, COUNT(*) as messages_count')
            ->where('sender_type', 'client')
            ->groupBy('client_phone')
            ->orderBy('last_message_time', 'desc')
            ->get();

        return view('dashboard.chats.index', compact('clientConversations', 'title'));
    }

    public function getConversation($clientPhone)
    {
        $messages = ChatMessage::where('client_phone', $clientPhone)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json(['messages' => $messages]);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'client_phone' => 'required|string',
            'message'      => 'required|string',
            'file'         => 'nullable|file|max:2048', // optional file upload
        ]);

        // 1) Save the message to the DB as an 'admin' message
        $chatMessage = ChatMessage::create([
            'client_phone' => $data['client_phone'],
            'message'      => $data['message'],
            'sender_type'  => 'admin',  // this distinguishes from 'bot' or 'client'
            'sender_id'    => Auth::id(),
        ]);

        // 2) Handle file upload (if provided)
        $fileUrl = null;
        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('chat_files', 'public');
            $chatMessage->update(['file_path' => $path]);

            // Build a public URL for the stored file
            // e.g., https://your-domain/storage/chat_files/filename.jpg
            $fileUrl = asset('storage/' . $path);
        }

        // 3) Broadcast event (for real-time updates if you have Echo or similar)
        broadcast(new \App\Events\ChatMessageSent($chatMessage))->toOthers();

        // 4) Optionally notify all admins (or a subset of users)
        $admins = User::all(); // or a custom query
        Notification::send($admins, new ChatNotification($chatMessage));

        // 5) Send a WhatsApp message using your Node.js service
        //    The Node endpoint typically is https://your-domain/send-message
        //    Adjust as needed, for example if you have: config('services.whatsapp.url') = 'https://maher511.site'
        //    Then the full endpoint might be: https://maher511.site/send-message
        try {
            $whatsappUrl = rtrim(config('services.whatsapp.url'), '/') . '/send-message';

            // Default payload for text-only
            $payload = [
                'phone'   => $data['client_phone'],    // no leading '+'
                'message' => $data['message'],
            ];

            // If there's a file, send it as an image + optional caption
            if ($fileUrl) {
                $payload['image']   = $fileUrl;
                $payload['caption'] = $data['message']; // You can customize
                // 'message' can be left out or used in "caption"
            }

            $response = Http::post($whatsappUrl, $payload);

            if ($response->failed()) {
                Log::error('Failed to send WhatsApp message', [
                    'client_phone' => $data['client_phone'],
                    'response'     => $response->body(),
                ]);
            } else {
                Log::info('WhatsApp message sent successfully', ['client_phone' => $data['client_phone']]);
            }
        } catch (\Exception $ex) {
            Log::error('Exception when sending WhatsApp message', [
                'client_phone' => $data['client_phone'],
                'exception'    => $ex->getMessage(),
            ]);
        }

        return response()->json(['message' => $chatMessage], 201);
    }
}
