<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\Log;

class WhatsAppAppointmentController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'client_phone' => 'required|string',
            'message'      => 'required|string',
        ]);

        $chatMessage = ChatMessage::create([
            'client_phone' => $data['client_phone'],
            'sender_type'  => 'client',
            'message'      => $data['message'],
        ]);

        return response()->json(['chatMessage' => $chatMessage], 201);
    }

}
