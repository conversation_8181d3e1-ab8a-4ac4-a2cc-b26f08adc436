<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waiting_tickets', function (Blueprint $table) {
            $table->id();
            $table->string('client_phone');
            $table->string('full_name');
            $table->date('birth_date')->nullable();
            $table->string('identity_number')->nullable();
            $table->string('clinic')->nullable();
            $table->string('status')->default('waiting');  // e.g., 'waiting', 'assigned'
            $table->string('waiting_number')->nullable();  // assigned by admin later
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waiting_tickets');
    }
};
