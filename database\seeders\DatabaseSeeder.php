<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // 1) Ensure roles & permissions exist before creating users
        $this->call(RolesAndPermissionsSeeder::class);

        // 2) Create users & assign them roles
        $this->call(UsersTableSeeder::class);

        // 3) Generate appointments
        // $this->call(AppointmentsTableSeeder::class);

        // 4) (Optional) Generate logs if not using Observers or if you just want extra logs
        // $this->call(LogsTableSeeder::class);
        // $this->call(ChatMessagesSeeder::class);
        $this->command->info('Database seeding completed!');
    }
}
