<?php

namespace Database\Factories;

use App\Models\Log;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class LogFactory extends Factory
{
    protected $model = Log::class;

    public function definition()
    {
        // Example action_type: "appointment.created", "appointment.updated", etc.
        $actionTypes = ['appointment.created', 'appointment.updated', 'user.created', 'user.updated'];

        return [
            'user_id'     => User::factory(), // will create a new user if not passed
            'action_type' => $this->faker->randomElement($actionTypes),
            'old_values'  => null, // or fake them if you wish
            'new_values'  => json_encode([
                'status' => $this->faker->randomElement(['pending', 'confirmed', 'rejected']),
            ]),
        ];
    }
}
