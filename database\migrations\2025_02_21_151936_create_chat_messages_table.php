<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration creates the chat_messages table to store messages from WhatsApp clients and replies
     * from the admin (or AI). The sender_type column indicates the origin of the message.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();

            // If the message is sent by an admin or AI, we store the user id; otherwise null for client messages.
            $table->unsignedBigInteger('sender_id')->nullable();

            // A string flag to determine the type of sender: 'client', 'admin', or 'ai'
            $table->string('sender_type');

            // Optionally, you could use receiver_id if needed, for admin-to-admin or admin responses.
            $table->unsignedBigInteger('receiver_id')->nullable();

            // Store the client phone number (used for WhatsApp messages)
            $table->string('client_phone');

            // The message text
            $table->text('message');

            // Optional file attachment (e.g., images or documents)
            $table->string('file_path')->nullable();

            $table->timestamps();

            // If sender_id and receiver_id reference users, you can add foreign key constraints:
            // $table->foreign('sender_id')->references('id')->on('users')->onDelete('set null');
            // $table->foreign('receiver_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_messages');
    }
}
