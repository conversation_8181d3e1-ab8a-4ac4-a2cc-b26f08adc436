<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="flex items-center space-x-4 py-5 lg:py-6">
            <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl">
                {{ $title }}
            </h2>
        </div>

        <div class="grid grid-cols-12 gap-4 sm:gap-5 lg:gap-6">
            <div class="col-span-12 lg:col-span-4">
                <div class="card p-4 sm:p-5">
                    <div class="flex items-center space-x-4">
                        <div class="avatar size-14">
                            <img class="rounded-full" src="{{ asset('images/200x200.png') }}" alt="avatar" />
                        </div>
                        <div>
                            <h3 class="text-base font-medium text-slate-700 dark:text-navy-100">
                                {{ auth()->user()->name }}
                            </h3>
                            <p class="text-xs-plus">
                                {{ auth()->user()->getRoleNames()->first() }}
                            </p>
                        </div>
                    </div>
                    <ul class="mt-6 space-y-1.5 font-inter font-medium">
                        <li>
                            <a class="flex items-center space-x-2 rounded-lg bg-primary px-4 py-2.5 tracking-wide text-white outline-hidden transition-all dark:bg-accent"
                               href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round"
                                          stroke-linejoin="round"
                                          stroke-width="1.5"
                                          d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>{{ $title }}</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <form class="col-span-12 lg:col-span-8" action="{{ route('settings.update') }}" method="POST">
                @csrf
                <div class="card">
                    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
                        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
                            {{ $title }}
                        </h2>
                        <div class="flex justify-center space-x-2">
                            <button type="submit"
                                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white
                                       hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90
                                       dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus
                                       dark:active:bg-accent/90">
                                Save
                            </button>
                        </div>
                    </div>

                    <div class="p-4 sm:p-5">
                        <!-- linktree -->
                        <x-input-text
                            :label="__('Linktree')"
                            name="linktree"
                            :value="old('linktree', $settings['linktree'] ?? '')"
                        />
                    </div>
                </div>
            </form>
        </div>
    </main>
</x-app-layout>
