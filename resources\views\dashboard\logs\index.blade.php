<x-app-layout :title="$title" is-header-blur="true">
    <main class="main-content w-full pb-8">
        <div
            class="mt-4 grid grid-cols-12 gap-4 px-[var(--margin-x)] transition-all duration-[.25s] sm:mt-5 sm:gap-5 lg:mt-6 lg:gap-6">
            <div class="card col-span-12">
                <div class="mt-3 flex items-center justify-between px-4 sm:px-5">
                    <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">{{ $title }}</h2>
                </div>
                <div class="card p-4 sm:p-5 shadow-lg rounded-lg overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-200 dark:divide-navy-500 text-center">
                        <thead class="bg-slate-50 dark:bg-navy-700">
                            <tr>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __("User") }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __("Action") }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __("Change") }}</th>
                                <th class="px-4 py-3 text-sm font-medium text-slate-600 dark:text-navy-100">
                                    {{ __("Date") }}</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-200 dark:divide-navy-500">
                            @foreach ($logs as $log)
                                <tr>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        {{ optional($log->user)->name ?? 'N/A' }}</td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">{{ $log->action_type }}</td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        @php
                                            $decodedOld = json_decode($log->old_values, true);
                                            $decodedNew = json_decode($log->new_values, true);
                                        @endphp
                                        @if ($decodedOld && $decodedNew)
                                            @foreach ($decodedNew as $field => $newValue)
                                                @if (isset($decodedOld[$field]) && $decodedOld[$field] != $newValue)
                                                    <div>
                                                        <strong>{{ ucfirst($field) }} updated:</strong>
                                                        from <span class="text-red-500">{{ $decodedOld[$field] }}</span>
                                                        to <span class="text-green-500">{{ $newValue }}</span>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @else
                                            N/A
                                        @endif
                                    </td>
                                    <td class="px-4 py-3 text-sm dark:text-navy-100">
                                        {{ $log->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $logs->links() }}
                    </div>
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
