<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * - sender_id: ID of the user (admin/AI) if applicable; null if sent from the client.
     * - sender_type: 'client', 'admin', or 'ai'
     * - receiver_id: ID of the recipient (if needed).
     * - client_phone: The client's WhatsApp phone number.
     * - message: The text of the message.
     * - file_path: Optional file attachment.
     *
     * @var array
     */
    protected $fillable = [
        'sender_id',
        'sender_type',
        'receiver_id',
        'client_phone',
        'message',
        'file_path',
    ];

    /**
     * Optionally, define relationships.
     * For example, if the sender is a User:
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }
}
