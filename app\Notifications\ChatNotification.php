<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Broadcasting\PrivateChannel;

class ChatNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $chatMessage;

    /**
     * Create a new notification instance.
     */
    public function __construct($chatMessage)
    {
        $this->chatMessage = $chatMessage;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        // We use both database and broadcast channels
        return ['database', 'broadcast'];
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toDatabase($notifiable)
    {
        return [
            'client_phone' => $this->chatMessage->client_phone,
            'message'      => $this->chatMessage->message,
            'sender_type'  => $this->chatMessage->sender_type,
            'timestamp'    => $this->chatMessage->created_at->toDateTimeString(),
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'client_phone' => $this->chatMessage->client_phone,
            'message'      => $this->chatMessage->message,
            'sender_type'  => $this->chatMessage->sender_type,
            'timestamp'    => $this->chatMessage->created_at->toDateTimeString(),
        ]);
    }

    /**
     * (Optional) Specify a custom channel name.
     */
    public function broadcastOn()
    {
        // For private channels use PrivateChannel. For now, we can use a public channel:
        return new PrivateChannel('notifications');
    }
}
