import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/ar/css/app.css',
                'resources/ar/js/app.js'
            ],
            refresh: false,
        }),
    ],
    server: {
        watch: {
            // Ignore changes in the whatsapp folder so Vite doesn't reload when those files change
            ignored: ["app/backend/server/whatsapp/whatsapp.js"],
        },
    },
});
