<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRatingReminderCountToWaitingTicketsTable extends Migration
{
    public function up()
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->unsignedInteger('rating_reminder_count')->default(0)->after('waiting_number');
        });
    }

    public function down()
    {
        Schema::table('waiting_tickets', function (Blueprint $table) {
            $table->dropColumn('rating_reminder_count');
        });
    }
}
