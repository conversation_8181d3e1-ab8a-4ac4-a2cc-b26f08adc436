<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        // 1) Create a few fixed users
        $superAdmin = User::create([
            'name'  => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin')
        ]);
        $superAdmin->assignRole('Super Admin');

        // $receptionist = User::factory()->create([
        //     'name'  => 'Receptionist User',
        //     'email' => '<EMAIL>',
        // ]);
        // $receptionist->assignRole('Receptionist');

        // $doctor = User::factory()->create([
        //     'name'  => 'Doctor User',
        //     'email' => '<EMAIL>',
        // ]);
        // $doctor->assignRole('Doctor');

        // // 2) Optionally create additional random users
        // //    Each will get a random role
        // $roles = ['Super Admin', 'Receptionist', 'Doctor'];
        // User::factory(10)->create()->each(function ($user) use ($roles) {
        //     $randomRole = $roles[array_rand($roles)];
        //     $user->assignRole($randomRole);
        // });

        $this->command->info('Users seeded (with various roles).');
    }
}
