<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class OfferController extends Controller
{
    public function index()
    {
        $title = __("Offers");
        $offers = Offer::paginate();
        return view("dashboard.offers.index", compact('title', 'offers'));
    }
    public function create(Request $request)
    {
        $title = __("Create New Offer");
        return view('dashboard.offers.create', compact('title'));
    }
    /**
     * Store a newly created offer in storage.
     */
    public function store(Request $request)
    {
        // Validate the input including image file.
        $request->validate([
            'title'       => 'required|string|max:255',
            'description' => 'required|string',
            'price'       => 'required|numeric|min:0',
            'image'       => 'required|image|max:2048', // maximum 2MB
        ]);

        // Handle file upload
        $imagePath = $request->file('image')->store('offers', 'public');

        // Create the offer.
        Offer::create([
            'title'       => $request->title,
            'description' => $request->description,
            'price'       => $request->price,
            'image'       => Storage::url($imagePath), // Save the public URL.
        ]);

        return redirect()->route('clinic-offers.index')->with('success', 'تم إنشاء العرض بنجاح.');
    }

    public function edit(Offer $clinic_offer)
    {
        $title = __("Edit Offer");
        return view("dashboard.offers.edit", compact('clinic_offer', 'title'));
    }

    /**
     * Update the specified offer in storage.
     */
    public function update(Request $request, Offer $clinic_offer)
    {
        // Validate the input; image is optional on update.
        $request->validate([
            'title'       => 'required|string|max:255',
            'description' => 'required|string',
            'price'       => 'required|numeric|min:0',
            'image'       => 'nullable|image|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'price']);

        // If an image is uploaded, handle the file upload.
        if ($request->hasFile('image')) {
            // Optionally delete the old image if needed.
            if ($clinic_offer->image) {
                $oldPath = str_replace('/storage/', '', $clinic_offer->image);
                Storage::disk('public')->delete($oldPath);
            }
            $imagePath = $request->file('image')->store('offers', 'public');
            $data['image'] = Storage::url($imagePath);
        }

        $clinic_offer->update($data);

        return redirect()->route('clinic-offers.index')->with('success', 'تم تحديث العرض بنجاح.');
    }

    /**
     * Remove the specified offer from storage.
     */
    public function destroy(Offer $clinic_offer)
    {
        // Optionally delete the image file from storage.
        if ($clinic_offer->image) {
            $oldPath = str_replace('/storage/', '', $clinic_offer->image);
            Storage::disk('public')->delete($oldPath);
        }

        $clinic_offer->delete();
        return redirect()->route('clinic-offers.index')->with('success', 'تم حذف العرض بنجاح.');
    }
}
